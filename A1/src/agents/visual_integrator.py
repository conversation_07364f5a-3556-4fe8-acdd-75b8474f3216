"""
Visual Integration Agent
Specialized agent for generating and integrating visual elements
"""

import re
import json
import base64
from typing import Dict, Any, List, Optional
from pathlib import Path
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import seaborn as sns
import numpy as np
from io import BytesIO

from crewai.tools import BaseTool
from pydantic import BaseModel, Field

class DiagramGeneratorTool(BaseTool):
    """Tool for generating technical diagrams and system architecture visuals"""
    name: str = "diagram_generator"
    description: str = "Generate relevant technical diagrams and system architecture visuals"

    def _run(self, content: str, diagram_type: str = "auto") -> Dict[str, Any]:
        """Generate diagrams based on content analysis"""
        try:
            if diagram_type == "auto":
                diagram_type = self._detect_diagram_type(content)

            diagram_data = self._generate_diagram(content, diagram_type)

            return {
                "diagram_type": diagram_type,
                "diagram_data": diagram_data,
                "integration_points": self._identify_integration_points(content),
                "visual_score": self._calculate_visual_score(content, diagram_data)
            }
        except Exception as e:
            return {"error": f"Diagram generation failed: {str(e)}"}

    def _detect_diagram_type(self, content: str) -> str:
        """Detect appropriate diagram type based on content"""
        content_lower = content.lower()

        if any(word in content_lower for word in ['database', 'table', 'schema', 'sql']):
            return 'database_schema'
        elif any(word in content_lower for word in ['architecture', 'system', 'component', 'module']):
            return 'system_architecture'
        elif any(word in content_lower for word in ['workflow', 'process', 'step', 'procedure']):
            return 'workflow'
        elif any(word in content_lower for word in ['ui', 'interface', 'screen', 'page']):
            return 'ui_mockup'
        elif any(word in content_lower for word in ['network', 'server', 'client', 'api']):
            return 'network_diagram'
        else:
            return 'flowchart'

    def _generate_diagram(self, content: str, diagram_type: str) -> Dict[str, Any]:
        """Generate specific diagram based on type"""
        if diagram_type == 'database_schema':
            return self._generate_database_schema(content)
        elif diagram_type == 'system_architecture':
            return self._generate_system_architecture(content)
        elif diagram_type == 'workflow':
            return self._generate_workflow_diagram(content)
        elif diagram_type == 'ui_mockup':
            return self._generate_ui_mockup(content)
        elif diagram_type == 'network_diagram':
            return self._generate_network_diagram(content)
        else:
            return self._generate_flowchart(content)

    def _generate_database_schema(self, content: str) -> Dict[str, Any]:
        """Generate database schema diagram"""
        # Extract table information from content
        tables = self._extract_table_info(content)

        fig, ax = plt.subplots(1, 1, figsize=(12, 8))
        ax.set_xlim(0, 10)
        ax.set_ylim(0, 8)
        ax.axis('off')

        # Draw tables
        table_positions = [(2, 6), (6, 6), (2, 3), (6, 3)]

        for i, (table_name, fields) in enumerate(tables.items()):
            if i >= len(table_positions):
                break

            x, y = table_positions[i]

            # Draw table box
            table_rect = patches.Rectangle((x-1, y-1), 2, 1.5,
                                         linewidth=2, edgecolor='blue',
                                         facecolor='lightblue', alpha=0.7)
            ax.add_patch(table_rect)

            # Add table name
            ax.text(x, y+0.2, table_name, ha='center', va='center',
                   fontsize=10, fontweight='bold')

            # Add fields
            for j, field in enumerate(fields[:3]):  # Show max 3 fields
                ax.text(x, y-0.2-j*0.2, field, ha='center', va='center',
                       fontsize=8)

        # Add relationships
        if len(tables) >= 2:
            ax.annotate('', xy=(5, 6.5), xytext=(3, 6.5),
                       arrowprops=dict(arrowstyle='->', lw=1.5, color='red'))

        plt.title('Database Schema Diagram', fontsize=14, fontweight='bold')

        # Save to base64
        buffer = BytesIO()
        plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
        buffer.seek(0)
        image_base64 = base64.b64encode(buffer.getvalue()).decode()
        plt.close()

        return {
            "image_base64": image_base64,
            "description": "Database schema showing table relationships",
            "tables_identified": list(tables.keys()),
            "caption": "Figure 1: Database Schema Design"
        }

    def _generate_system_architecture(self, content: str) -> Dict[str, Any]:
        """Generate system architecture diagram"""
        components = self._extract_system_components(content)

        fig, ax = plt.subplots(1, 1, figsize=(12, 8))
        ax.set_xlim(0, 10)
        ax.set_ylim(0, 8)
        ax.axis('off')

        # Define component positions
        positions = {
            'frontend': (2, 6),
            'backend': (5, 6),
            'database': (8, 6),
            'api': (5, 4),
            'server': (5, 2)
        }

        colors = {
            'frontend': 'lightgreen',
            'backend': 'lightblue',
            'database': 'lightyellow',
            'api': 'lightcoral',
            'server': 'lightgray'
        }

        # Draw components
        for component in components:
            if component in positions:
                x, y = positions[component]
                color = colors.get(component, 'lightgray')

                # Draw component box
                comp_rect = patches.Rectangle((x-0.8, y-0.5), 1.6, 1,
                                            linewidth=2, edgecolor='black',
                                            facecolor=color, alpha=0.8)
                ax.add_patch(comp_rect)

                # Add component label
                ax.text(x, y, component.title(), ha='center', va='center',
                       fontsize=10, fontweight='bold')

        # Add connections
        if 'frontend' in components and 'backend' in components:
            ax.annotate('', xy=(4.2, 6), xytext=(2.8, 6),
                       arrowprops=dict(arrowstyle='<->', lw=2, color='blue'))

        if 'backend' in components and 'database' in components:
            ax.annotate('', xy=(7.2, 6), xytext=(5.8, 6),
                       arrowprops=dict(arrowstyle='<->', lw=2, color='green'))

        plt.title('System Architecture Diagram', fontsize=14, fontweight='bold')

        # Save to base64
        buffer = BytesIO()
        plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
        buffer.seek(0)
        image_base64 = base64.b64encode(buffer.getvalue()).decode()
        plt.close()

        return {
            "image_base64": image_base64,
            "description": "System architecture showing component relationships",
            "components_identified": components,
            "caption": "Figure 2: System Architecture Overview"
        }

    def _generate_workflow_diagram(self, content: str) -> Dict[str, Any]:
        """Generate workflow/process diagram"""
        steps = self._extract_workflow_steps(content)

        fig, ax = plt.subplots(1, 1, figsize=(12, 8))
        ax.set_xlim(0, 10)
        ax.set_ylim(0, 8)
        ax.axis('off')

        # Draw workflow steps
        step_positions = [(2, 6), (5, 6), (8, 6), (5, 4), (5, 2)]

        for i, step in enumerate(steps[:5]):  # Max 5 steps
            if i < len(step_positions):
                x, y = step_positions[i]

                # Draw step box
                step_rect = patches.Rectangle((x-0.8, y-0.4), 1.6, 0.8,
                                            linewidth=2, edgecolor='purple',
                                            facecolor='lavender', alpha=0.8)
                ax.add_patch(step_rect)

                # Add step text
                step_text = step[:15] + "..." if len(step) > 15 else step
                ax.text(x, y, step_text, ha='center', va='center',
                       fontsize=9, fontweight='bold')

                # Add arrows between steps
                if i < len(steps) - 1 and i < len(step_positions) - 1:
                    next_x, next_y = step_positions[i + 1]
                    if y == next_y:  # Horizontal arrow
                        ax.annotate('', xy=(next_x-0.8, next_y), xytext=(x+0.8, y),
                                   arrowprops=dict(arrowstyle='->', lw=2, color='purple'))
                    else:  # Vertical arrow
                        ax.annotate('', xy=(next_x, next_y+0.4), xytext=(x, y-0.4),
                                   arrowprops=dict(arrowstyle='->', lw=2, color='purple'))

        plt.title('Workflow Process Diagram', fontsize=14, fontweight='bold')

        # Save to base64
        buffer = BytesIO()
        plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
        buffer.seek(0)
        image_base64 = base64.b64encode(buffer.getvalue()).decode()
        plt.close()

        return {
            "image_base64": image_base64,
            "description": "Workflow diagram showing process steps",
            "steps_identified": steps,
            "caption": "Figure 3: Process Workflow"
        }

    def _generate_ui_mockup(self, content: str) -> Dict[str, Any]:
        """Generate UI mockup diagram"""
        ui_elements = self._extract_ui_elements(content)

        fig, ax = plt.subplots(1, 1, figsize=(10, 12))
        ax.set_xlim(0, 8)
        ax.set_ylim(0, 10)
        ax.axis('off')

        # Draw browser frame
        browser_rect = patches.Rectangle((1, 1), 6, 8,
                                       linewidth=3, edgecolor='gray',
                                       facecolor='white', alpha=1)
        ax.add_patch(browser_rect)

        # Draw header
        header_rect = patches.Rectangle((1.2, 8.2), 5.6, 0.6,
                                      linewidth=1, edgecolor='blue',
                                      facecolor='lightblue', alpha=0.8)
        ax.add_patch(header_rect)
        ax.text(4, 8.5, 'Header / Navigation', ha='center', va='center',
               fontsize=10, fontweight='bold')

        # Draw main content area
        content_rect = patches.Rectangle((1.2, 3), 5.6, 5,
                                       linewidth=1, edgecolor='green',
                                       facecolor='lightgreen', alpha=0.3)
        ax.add_patch(content_rect)
        ax.text(4, 5.5, 'Main Content Area', ha='center', va='center',
               fontsize=12, fontweight='bold')

        # Draw sidebar if mentioned
        if any('sidebar' in elem.lower() for elem in ui_elements):
            sidebar_rect = patches.Rectangle((1.2, 3), 1.5, 5,
                                           linewidth=1, edgecolor='orange',
                                           facecolor='lightyellow', alpha=0.8)
            ax.add_patch(sidebar_rect)
            ax.text(1.95, 5.5, 'Sidebar', ha='center', va='center',
                   fontsize=9, rotation=90)

        # Draw footer
        footer_rect = patches.Rectangle((1.2, 1.2), 5.6, 0.6,
                                      linewidth=1, edgecolor='red',
                                      facecolor='lightcoral', alpha=0.8)
        ax.add_patch(footer_rect)
        ax.text(4, 1.5, 'Footer', ha='center', va='center',
               fontsize=10, fontweight='bold')

        plt.title('User Interface Mockup', fontsize=14, fontweight='bold')

        # Save to base64
        buffer = BytesIO()
        plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
        buffer.seek(0)
        image_base64 = base64.b64encode(buffer.getvalue()).decode()
        plt.close()

        return {
            "image_base64": image_base64,
            "description": "User interface mockup design",
            "ui_elements": ui_elements,
            "caption": "Figure 4: User Interface Design"
        }

    def _generate_network_diagram(self, content: str) -> Dict[str, Any]:
        """Generate network diagram"""
        return self._generate_system_architecture(content)  # Similar to system architecture

    def _generate_flowchart(self, content: str) -> Dict[str, Any]:
        """Generate generic flowchart"""
        return self._generate_workflow_diagram(content)  # Similar to workflow

    def _extract_table_info(self, content: str) -> Dict[str, List[str]]:
        """Extract table information from content"""
        tables = {}

        # Look for table mentions
        table_patterns = [
            r'table[s]?\s+(\w+)',
            r'(\w+)\s+table',
            r'CREATE\s+TABLE\s+(\w+)',
            r'(\w+)\s+entity'
        ]

        for pattern in table_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                table_name = match.strip()
                if table_name and len(table_name) > 2:
                    tables[table_name] = ['id', 'name', 'created_at']

        # Default tables if none found
        if not tables:
            tables = {
                'users': ['id', 'email', 'name'],
                'reports': ['id', 'title', 'content'],
                'sessions': ['id', 'user_id', 'token']
            }

        return tables

    def _extract_system_components(self, content: str) -> List[str]:
        """Extract system components from content"""
        components = []

        component_keywords = {
            'frontend': ['frontend', 'front-end', 'client', 'ui', 'interface'],
            'backend': ['backend', 'back-end', 'server', 'api'],
            'database': ['database', 'db', 'storage', 'data'],
            'api': ['api', 'endpoint', 'service'],
            'server': ['server', 'host', 'deployment']
        }

        content_lower = content.lower()

        for component, keywords in component_keywords.items():
            if any(keyword in content_lower for keyword in keywords):
                components.append(component)

        return components if components else ['frontend', 'backend', 'database']

    def _extract_workflow_steps(self, content: str) -> List[str]:
        """Extract workflow steps from content"""
        steps = []

        # Look for numbered steps
        step_patterns = [
            r'\d+\.\s+([^.]+)',
            r'step\s+\d+[:\s]+([^.]+)',
            r'first[ly]?\s*[,:]?\s*([^.]+)',
            r'then[,:]?\s*([^.]+)',
            r'next[,:]?\s*([^.]+)',
            r'finally[,:]?\s*([^.]+)'
        ]

        for pattern in step_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                step = match.strip()
                if step and len(step) > 5:
                    steps.append(step)

        # Default steps if none found
        if not steps:
            steps = ['Start', 'Process', 'Validate', 'Complete']

        return steps[:5]  # Max 5 steps

    def _extract_ui_elements(self, content: str) -> List[str]:
        """Extract UI elements from content"""
        elements = []

        ui_keywords = [
            'button', 'form', 'input', 'menu', 'navigation', 'sidebar',
            'header', 'footer', 'modal', 'dropdown', 'table', 'list'
        ]

        content_lower = content.lower()

        for keyword in ui_keywords:
            if keyword in content_lower:
                elements.append(keyword)

        return elements if elements else ['header', 'navigation', 'content', 'footer']

    def _identify_integration_points(self, content: str) -> List[str]:
        """Identify where visuals should be integrated"""
        integration_points = []

        # Look for sections that would benefit from visuals
        visual_indicators = [
            'architecture', 'design', 'structure', 'workflow', 'process',
            'system', 'database', 'interface', 'diagram', 'chart'
        ]

        sentences = content.split('.')
        for i, sentence in enumerate(sentences):
            if any(indicator in sentence.lower() for indicator in visual_indicators):
                integration_points.append(f"After sentence {i+1}: {sentence.strip()[:50]}...")

        return integration_points

    def _calculate_visual_score(self, content: str, diagram_data: Dict[str, Any]) -> float:
        """Calculate visual enhancement score"""
        score = 50.0  # Base score for having a visual

        # Bonus for relevant diagram type
        if diagram_data.get("components_identified") or diagram_data.get("tables_identified"):
            score += 30.0

        # Bonus for multiple integration points
        integration_points = self._identify_integration_points(content)
        score += min(20.0, len(integration_points) * 5)

        return min(100.0, score)

class ChartCreatorTool(BaseTool):
    """Tool for creating charts and data visualizations"""
    name: str = "chart_creator"
    description: str = "Create charts and data visualizations to support content"

    def _run(self, content: str, chart_type: str = "auto") -> Dict[str, Any]:
        """Create charts based on content analysis"""
        try:
            if chart_type == "auto":
                chart_type = self._detect_chart_type(content)

            chart_data = self._generate_chart(content, chart_type)

            return {
                "chart_type": chart_type,
                "chart_data": chart_data,
                "data_points": self._extract_data_points(content),
                "chart_score": self._calculate_chart_score(content, chart_data)
            }
        except Exception as e:
            return {"error": f"Chart creation failed: {str(e)}"}

    def _detect_chart_type(self, content: str) -> str:
        """Detect appropriate chart type based on content"""
        content_lower = content.lower()

        if any(word in content_lower for word in ['performance', 'speed', 'time', 'duration']):
            return 'performance_chart'
        elif any(word in content_lower for word in ['progress', 'timeline', 'schedule']):
            return 'timeline_chart'
        elif any(word in content_lower for word in ['comparison', 'vs', 'versus', 'compare']):
            return 'comparison_chart'
        elif any(word in content_lower for word in ['distribution', 'percentage', 'ratio']):
            return 'pie_chart'
        else:
            return 'bar_chart'

    def _generate_chart(self, content: str, chart_type: str) -> Dict[str, Any]:
        """Generate specific chart based on type"""
        if chart_type == 'performance_chart':
            return self._generate_performance_chart(content)
        elif chart_type == 'timeline_chart':
            return self._generate_timeline_chart(content)
        elif chart_type == 'comparison_chart':
            return self._generate_comparison_chart(content)
        elif chart_type == 'pie_chart':
            return self._generate_pie_chart(content)
        else:
            return self._generate_bar_chart(content)

    def _generate_performance_chart(self, content: str) -> Dict[str, Any]:
        """Generate performance metrics chart"""
        # Sample performance data
        metrics = ['Load Time', 'Response Time', 'Throughput', 'Error Rate']
        before_values = [3.2, 1.8, 85, 5.2]
        after_values = [1.1, 0.6, 150, 1.1]

        fig, ax = plt.subplots(1, 1, figsize=(10, 6))

        x = np.arange(len(metrics))
        width = 0.35

        bars1 = ax.bar(x - width/2, before_values, width, label='Before Optimization',
                      color='lightcoral', alpha=0.8)
        bars2 = ax.bar(x + width/2, after_values, width, label='After Optimization',
                      color='lightgreen', alpha=0.8)

        ax.set_xlabel('Performance Metrics')
        ax.set_ylabel('Values')
        ax.set_title('Performance Improvement Analysis')
        ax.set_xticks(x)
        ax.set_xticklabels(metrics)
        ax.legend()

        # Add value labels on bars
        for bars in [bars1, bars2]:
            for bar in bars:
                height = bar.get_height()
                ax.annotate(f'{height}',
                           xy=(bar.get_x() + bar.get_width() / 2, height),
                           xytext=(0, 3),
                           textcoords="offset points",
                           ha='center', va='bottom')

        plt.tight_layout()

        # Save to base64
        buffer = BytesIO()
        plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
        buffer.seek(0)
        image_base64 = base64.b64encode(buffer.getvalue()).decode()
        plt.close()

        return {
            "image_base64": image_base64,
            "description": "Performance metrics comparison chart",
            "metrics": metrics,
            "caption": "Figure 5: Performance Improvement Analysis"
        }

    def _generate_timeline_chart(self, content: str) -> Dict[str, Any]:
        """Generate project timeline chart"""
        # Sample timeline data
        phases = ['Planning', 'Design', 'Development', 'Testing', 'Deployment']
        durations = [2, 3, 8, 3, 1]  # weeks

        fig, ax = plt.subplots(1, 1, figsize=(12, 6))

        # Create Gantt-style chart
        start_pos = 0
        colors = ['#FF9999', '#66B2FF', '#99FF99', '#FFCC99', '#FF99CC']

        for i, (phase, duration) in enumerate(zip(phases, durations)):
            ax.barh(i, duration, left=start_pos, height=0.6,
                   color=colors[i], alpha=0.8, edgecolor='black')

            # Add phase labels
            ax.text(start_pos + duration/2, i, f'{phase}\n({duration}w)',
                   ha='center', va='center', fontweight='bold')

            start_pos += duration

        ax.set_yticks(range(len(phases)))
        ax.set_yticklabels(phases)
        ax.set_xlabel('Timeline (Weeks)')
        ax.set_title('Project Timeline and Phases')
        ax.grid(axis='x', alpha=0.3)

        plt.tight_layout()

        # Save to base64
        buffer = BytesIO()
        plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
        buffer.seek(0)
        image_base64 = base64.b64encode(buffer.getvalue()).decode()
        plt.close()

        return {
            "image_base64": image_base64,
            "description": "Project timeline and phases",
            "phases": phases,
            "caption": "Figure 6: Project Timeline"
        }

    def _generate_comparison_chart(self, content: str) -> Dict[str, Any]:
        """Generate comparison chart"""
        # Sample comparison data
        categories = ['Functionality', 'Performance', 'Usability', 'Maintainability']
        old_system = [6, 4, 5, 3]
        new_system = [9, 8, 8, 7]

        fig, ax = plt.subplots(1, 1, figsize=(10, 6))

        x = np.arange(len(categories))
        width = 0.35

        bars1 = ax.bar(x - width/2, old_system, width, label='Previous System',
                      color='lightblue', alpha=0.8)
        bars2 = ax.bar(x + width/2, new_system, width, label='Improved System',
                      color='lightgreen', alpha=0.8)

        ax.set_xlabel('System Aspects')
        ax.set_ylabel('Rating (1-10)')
        ax.set_title('System Improvement Comparison')
        ax.set_xticks(x)
        ax.set_xticklabels(categories)
        ax.legend()
        ax.set_ylim(0, 10)

        plt.tight_layout()

        # Save to base64
        buffer = BytesIO()
        plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
        buffer.seek(0)
        image_base64 = base64.b64encode(buffer.getvalue()).decode()
        plt.close()

        return {
            "image_base64": image_base64,
            "description": "System improvement comparison",
            "categories": categories,
            "caption": "Figure 7: System Comparison Analysis"
        }

    def _generate_pie_chart(self, content: str) -> Dict[str, Any]:
        """Generate pie chart for distributions"""
        # Sample distribution data
        labels = ['Frontend Development', 'Backend Development', 'Database Design', 'Testing', 'Documentation']
        sizes = [30, 25, 20, 15, 10]
        colors = ['#FF9999', '#66B2FF', '#99FF99', '#FFCC99', '#FF99CC']

        fig, ax = plt.subplots(1, 1, figsize=(8, 8))

        wedges, texts, autotexts = ax.pie(sizes, labels=labels, colors=colors,
                                         autopct='%1.1f%%', startangle=90)

        ax.set_title('Time Distribution Across Project Activities')

        plt.tight_layout()

        # Save to base64
        buffer = BytesIO()
        plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
        buffer.seek(0)
        image_base64 = base64.b64encode(buffer.getvalue()).decode()
        plt.close()

        return {
            "image_base64": image_base64,
            "description": "Time distribution across activities",
            "categories": labels,
            "caption": "Figure 8: Activity Time Distribution"
        }

    def _generate_bar_chart(self, content: str) -> Dict[str, Any]:
        """Generate generic bar chart"""
        # Sample data
        technologies = ['Python', 'JavaScript', 'SQL', 'HTML/CSS', 'Git']
        proficiency = [8, 7, 6, 8, 7]

        fig, ax = plt.subplots(1, 1, figsize=(10, 6))

        bars = ax.bar(technologies, proficiency, color='skyblue', alpha=0.8, edgecolor='navy')

        ax.set_xlabel('Technologies')
        ax.set_ylabel('Proficiency Level (1-10)')
        ax.set_title('Technology Skills Acquired')
        ax.set_ylim(0, 10)

        # Add value labels on bars
        for bar in bars:
            height = bar.get_height()
            ax.annotate(f'{height}',
                       xy=(bar.get_x() + bar.get_width() / 2, height),
                       xytext=(0, 3),
                       textcoords="offset points",
                       ha='center', va='bottom')

        plt.tight_layout()

        # Save to base64
        buffer = BytesIO()
        plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
        buffer.seek(0)
        image_base64 = base64.b64encode(buffer.getvalue()).decode()
        plt.close()

        return {
            "image_base64": image_base64,
            "description": "Technology skills proficiency",
            "technologies": technologies,
            "caption": "Figure 9: Skills Development"
        }

    def _extract_data_points(self, content: str) -> List[str]:
        """Extract potential data points from content"""
        data_points = []

        # Look for numbers and percentages
        number_patterns = [
            r'(\d+(?:\.\d+)?%)',
            r'(\d+(?:\.\d+)?\s*(?:seconds?|minutes?|hours?|days?))',
            r'(\d+(?:\.\d+)?\s*(?:users?|requests?|errors?))'
        ]

        for pattern in number_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            data_points.extend(matches)

        return data_points

    def _calculate_chart_score(self, content: str, chart_data: Dict[str, Any]) -> float:
        """Calculate chart relevance score"""
        score = 60.0  # Base score

        # Bonus for data points found
        data_points = self._extract_data_points(content)
        score += min(30.0, len(data_points) * 5)

        # Bonus for relevant chart type
        if chart_data.get("categories") or chart_data.get("metrics"):
            score += 10.0

        return min(100.0, score)

class VisualValidatorTool(BaseTool):
    """Tool for validating visual quality and relevance"""
    name: str = "visual_validator"
    description: str = "Validate visual quality and relevance to content"

    def _run(self, content: str, visuals: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Validate visual elements"""
        try:
            validation_results = []

            for visual in visuals:
                validation = self._validate_visual(content, visual)
                validation_results.append(validation)

            overall_score = sum(v["score"] for v in validation_results) / len(validation_results) if validation_results else 0

            return {
                "validation_results": validation_results,
                "overall_score": overall_score,
                "recommendations": self._generate_recommendations(validation_results)
            }
        except Exception as e:
            return {"error": f"Visual validation failed: {str(e)}"}

    def _validate_visual(self, content: str, visual: Dict[str, Any]) -> Dict[str, Any]:
        """Validate individual visual element"""
        score = 50.0  # Base score
        issues = []

        # Check relevance to content
        if self._is_relevant_to_content(content, visual):
            score += 30.0
        else:
            issues.append("Visual may not be directly relevant to content")

        # Check if visual has proper caption
        if visual.get("caption"):
            score += 10.0
        else:
            issues.append("Missing caption")

        # Check if visual has description
        if visual.get("description"):
            score += 10.0
        else:
            issues.append("Missing description")

        return {
            "visual_type": visual.get("chart_type", visual.get("diagram_type", "unknown")),
            "score": min(100.0, score),
            "issues": issues,
            "recommendations": self._get_visual_recommendations(visual, issues)
        }

    def _is_relevant_to_content(self, content: str, visual: Dict[str, Any]) -> bool:
        """Check if visual is relevant to content"""
        content_lower = content.lower()

        # Check for keywords related to visual type
        visual_type = visual.get("chart_type", visual.get("diagram_type", ""))

        if "performance" in visual_type and any(word in content_lower for word in ['performance', 'speed', 'optimization']):
            return True
        elif "timeline" in visual_type and any(word in content_lower for word in ['timeline', 'schedule', 'phases']):
            return True
        elif "database" in visual_type and any(word in content_lower for word in ['database', 'table', 'schema']):
            return True
        elif "system" in visual_type and any(word in content_lower for word in ['system', 'architecture', 'component']):
            return True

        return False

    def _get_visual_recommendations(self, visual: Dict[str, Any], issues: List[str]) -> List[str]:
        """Get recommendations for visual improvement"""
        recommendations = []

        if "Missing caption" in issues:
            recommendations.append("Add a descriptive caption that explains what the visual shows")

        if "Missing description" in issues:
            recommendations.append("Add a detailed description of the visual content")

        if "not be directly relevant" in str(issues):
            recommendations.append("Ensure visual directly supports the surrounding text content")

        return recommendations

    def _generate_recommendations(self, validation_results: List[Dict[str, Any]]) -> List[str]:
        """Generate overall recommendations"""
        recommendations = []

        low_score_visuals = [v for v in validation_results if v["score"] < 70]
        if low_score_visuals:
            recommendations.append(f"Improve {len(low_score_visuals)} visuals with low relevance scores")

        missing_captions = sum(1 for v in validation_results if "Missing caption" in v["issues"])
        if missing_captions > 0:
            recommendations.append(f"Add captions to {missing_captions} visuals")

        return recommendations

class CaptionGeneratorTool(BaseTool):
    """Tool for generating captions and references for visual elements"""
    name: str = "caption_generator"
    description: str = "Generate captions and references for visual elements"

    def _run(self, visuals: List[Dict[str, Any]], content: str) -> Dict[str, Any]:
        """Generate captions for visual elements"""
        try:
            captioned_visuals = []

            for i, visual in enumerate(visuals):
                caption = self._generate_caption(visual, i + 1)
                reference = self._generate_reference(visual, i + 1)

                captioned_visual = visual.copy()
                captioned_visual["generated_caption"] = caption
                captioned_visual["reference_text"] = reference

                captioned_visuals.append(captioned_visual)

            return {
                "captioned_visuals": captioned_visuals,
                "reference_list": self._generate_reference_list(captioned_visuals)
            }
        except Exception as e:
            return {"error": f"Caption generation failed: {str(e)}"}

    def _generate_caption(self, visual: Dict[str, Any], figure_number: int) -> str:
        """Generate caption for visual"""
        visual_type = visual.get("chart_type", visual.get("diagram_type", "visual"))
        description = visual.get("description", "")

        if "performance" in visual_type:
            return f"Figure {figure_number}: Performance metrics showing improvement across key indicators"
        elif "timeline" in visual_type:
            return f"Figure {figure_number}: Project timeline illustrating development phases and duration"
        elif "database" in visual_type:
            return f"Figure {figure_number}: Database schema design showing entity relationships"
        elif "system" in visual_type:
            return f"Figure {figure_number}: System architecture overview depicting component interactions"
        elif "comparison" in visual_type:
            return f"Figure {figure_number}: Comparative analysis of system improvements"
        elif "pie" in visual_type:
            return f"Figure {figure_number}: Distribution analysis of project activities"
        else:
            return f"Figure {figure_number}: {description}"

    def _generate_reference(self, visual: Dict[str, Any], figure_number: int) -> str:
        """Generate reference text for visual"""
        return f"As shown in Figure {figure_number}"

    def _generate_reference_list(self, visuals: List[Dict[str, Any]]) -> List[str]:
        """Generate list of figure references"""
        references = []

        for visual in visuals:
            caption = visual.get("generated_caption", visual.get("caption", ""))
            if caption:
                references.append(caption)

        return references

class VisualIntegrator:
    """Visual Integration Agent - main class"""

    def __init__(self):
        """Initialize visual integrator with tools"""
        self.tools = [
            DiagramGeneratorTool(),
            ChartCreatorTool(),
            VisualValidatorTool(),
            CaptionGeneratorTool()
        ]

    def get_tools(self) -> List[BaseTool]:
        """Get list of available tools"""
        return self.tools

    def integrate_visuals(self, content: str, focus_areas: List[str] = None) -> Dict[str, Any]:
        """Main method to integrate visual elements"""
        try:
            if not focus_areas:
                focus_areas = ['visual_elements']

            generated_visuals = []
            all_changes = []

            # Step 1: Generate diagrams
            if 'visual_elements' in focus_areas:
                diagram_tool = self.tools[0]
                diagram_result = diagram_tool._run(content)

                if "error" not in diagram_result:
                    generated_visuals.append(diagram_result)
                    all_changes.append("Generated technical diagram")

            # Step 2: Generate charts
            chart_tool = self.tools[1]
            chart_result = chart_tool._run(content)

            if "error" not in chart_result:
                generated_visuals.append(chart_result)
                all_changes.append("Generated data visualization chart")

            # Step 3: Validate visuals
            validator_tool = self.tools[2]
            validation_result = validator_tool._run(content, generated_visuals)

            # Step 4: Generate captions
            caption_tool = self.tools[3]
            caption_result = caption_tool._run(generated_visuals, content)

            if "error" not in caption_result:
                generated_visuals = caption_result["captioned_visuals"]
                all_changes.append("Generated captions and references")

            # Integrate visuals into content
            enhanced_content = self._integrate_visuals_into_content(content, generated_visuals)
            all_changes.append("Integrated visuals into document")

            # Compile final results
            final_result = {
                "enhanced_content": enhanced_content,
                "original_content": content,
                "generated_visuals": generated_visuals,
                "changes_made": all_changes,
                "focus_areas": focus_areas,
                "visual_count": len(generated_visuals),
                "validation_score": validation_result.get("overall_score", 0) if 'validation_result' in locals() else 0
            }

            return final_result

        except Exception as e:
            return {"error": f"Visual integration failed: {str(e)}"}

    def _integrate_visuals_into_content(self, content: str, visuals: List[Dict[str, Any]]) -> str:
        """Integrate visual elements into content"""
        enhanced_content = content

        # Add visual references at appropriate points
        paragraphs = enhanced_content.split('\n\n')
        enhanced_paragraphs = []

        visual_index = 0

        for i, paragraph in enumerate(paragraphs):
            enhanced_paragraphs.append(paragraph)

            # Insert visual after every 2-3 paragraphs
            if (i + 1) % 3 == 0 and visual_index < len(visuals):
                visual = visuals[visual_index]

                # Add visual reference
                caption = visual.get("generated_caption", visual.get("caption", f"Figure {visual_index + 1}"))
                reference = visual.get("reference_text", f"See Figure {visual_index + 1}")

                visual_insertion = f"\n\n{reference} below.\n\n[{caption}]\n\n"
                enhanced_paragraphs.append(visual_insertion)

                visual_index += 1

        return '\n\n'.join(enhanced_paragraphs)
