"""
Structure Optimization Agent
Specialized agent for reorganizing content and ensuring academic formatting standards
"""

import re
import json
from typing import Dict, Any, List, Optional
from pathlib import Path

from crewai.tools import BaseTool
from pydantic import BaseModel, Field

class StructureReorganizerTool(BaseTool):
    """Tool for reorganizing document structure"""
    name: str = "structure_reorganizer"
    description: str = "Reorganize document structure for optimal logical progression"
    
    def _run(self, content: str, sections: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Reorganize content structure"""
        try:
            if not sections:
                sections = self._identify_sections(content)
            
            reorganized_content, changes = self._reorganize_structure(content, sections)
            
            return {
                "reorganized_content": reorganized_content,
                "changes_made": changes,
                "section_count": len(sections),
                "structure_score": self._calculate_structure_score(content, reorganized_content)
            }
        except Exception as e:
            return {"error": f"Structure reorganization failed: {str(e)}"}
    
    def _identify_sections(self, content: str) -> List[Dict[str, Any]]:
        """Identify document sections"""
        sections = []
        lines = content.split('\n')
        current_section = None
        
        # Common heading patterns
        heading_patterns = [
            r'^#{1,6}\s+(.+)$',  # Markdown headings
            r'^([A-Z][A-Z\s]+)$',  # ALL CAPS headings
            r'^\d+\.?\s+([A-Z].+)$',  # Numbered headings
            r'^([A-Z][a-z\s]+):?\s*$'  # Title case headings
        ]
        
        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
                
            # Check if line matches heading patterns
            is_heading = False
            heading_text = ""
            
            for pattern in heading_patterns:
                match = re.match(pattern, line)
                if match:
                    is_heading = True
                    heading_text = match.group(1) if match.groups() else line
                    break
            
            if is_heading:
                # Save previous section
                if current_section:
                    sections.append(current_section)
                
                # Start new section
                current_section = {
                    "title": heading_text,
                    "start_line": i,
                    "content": "",
                    "word_count": 0,
                    "type": self._classify_section_type(heading_text)
                }
            elif current_section:
                current_section["content"] += line + "\n"
                current_section["word_count"] = len(current_section["content"].split())
        
        # Add last section
        if current_section:
            sections.append(current_section)
        
        return sections
    
    def _classify_section_type(self, title: str) -> str:
        """Classify section type based on title"""
        title_lower = title.lower()
        
        if any(word in title_lower for word in ['introduction', 'intro', 'overview']):
            return 'introduction'
        elif any(word in title_lower for word in ['background', 'literature', 'theory']):
            return 'background'
        elif any(word in title_lower for word in ['method', 'approach', 'design']):
            return 'methodology'
        elif any(word in title_lower for word in ['implementation', 'development', 'work']):
            return 'implementation'
        elif any(word in title_lower for word in ['result', 'outcome', 'finding']):
            return 'results'
        elif any(word in title_lower for word in ['discussion', 'analysis', 'evaluation']):
            return 'discussion'
        elif any(word in title_lower for word in ['conclusion', 'summary', 'final']):
            return 'conclusion'
        elif any(word in title_lower for word in ['reference', 'bibliography']):
            return 'references'
        else:
            return 'content'
    
    def _reorganize_structure(self, content: str, sections: List[Dict[str, Any]]) -> tuple[str, List[str]]:
        """Reorganize sections for optimal flow"""
        changes = []
        
        # Define ideal section order
        ideal_order = [
            'introduction', 'background', 'methodology', 
            'implementation', 'results', 'discussion', 
            'conclusion', 'references'
        ]
        
        # Sort sections according to ideal order
        sorted_sections = []
        used_sections = set()
        
        # First, add sections in ideal order
        for section_type in ideal_order:
            for section in sections:
                if section['type'] == section_type and section['title'] not in used_sections:
                    sorted_sections.append(section)
                    used_sections.add(section['title'])
                    break
        
        # Add remaining sections
        for section in sections:
            if section['title'] not in used_sections:
                sorted_sections.append(section)
        
        # Rebuild content
        reorganized_parts = []
        for section in sorted_sections:
            # Add section heading
            reorganized_parts.append(f"## {section['title']}")
            reorganized_parts.append("")
            
            # Add section content
            reorganized_parts.append(section['content'].strip())
            reorganized_parts.append("")
        
        reorganized_content = '\n'.join(reorganized_parts)
        changes.append(f"Reorganized {len(sections)} sections for optimal flow")
        
        return reorganized_content, changes
    
    def _calculate_structure_score(self, original: str, reorganized: str) -> float:
        """Calculate structure improvement score"""
        # Count sections
        original_sections = len(re.findall(r'^#{1,6}\s+', original, re.MULTILINE))
        reorganized_sections = len(re.findall(r'^#{1,6}\s+', reorganized, re.MULTILINE))
        
        # Score based on section organization
        if reorganized_sections >= 5:
            return 90.0
        elif reorganized_sections >= 3:
            return 75.0
        else:
            return 60.0

class FormatValidatorTool(BaseTool):
    """Tool for validating and ensuring academic formatting standards"""
    name: str = "format_validator"
    description: str = "Validate and ensure compliance with academic formatting standards"
    
    def _run(self, content: str) -> Dict[str, Any]:
        """Validate and fix formatting"""
        try:
            formatted_content, changes = self._apply_academic_formatting(content)
            
            return {
                "formatted_content": formatted_content,
                "changes_made": changes,
                "compliance_score": self._calculate_compliance_score(content, formatted_content)
            }
        except Exception as e:
            return {"error": f"Format validation failed: {str(e)}"}
    
    def _apply_academic_formatting(self, content: str) -> tuple[str, List[str]]:
        """Apply academic formatting standards"""
        changes = []
        formatted_content = content
        
        # Ensure proper heading hierarchy
        formatted_content, heading_changes = self._fix_heading_hierarchy(formatted_content)
        changes.extend(heading_changes)
        
        # Fix paragraph spacing
        formatted_content, spacing_changes = self._fix_paragraph_spacing(formatted_content)
        changes.extend(spacing_changes)
        
        # Ensure proper citation format (basic)
        formatted_content, citation_changes = self._fix_citations(formatted_content)
        changes.extend(citation_changes)
        
        # Fix list formatting
        formatted_content, list_changes = self._fix_list_formatting(formatted_content)
        changes.extend(list_changes)
        
        return formatted_content, changes
    
    def _fix_heading_hierarchy(self, content: str) -> tuple[str, List[str]]:
        """Fix heading hierarchy"""
        changes = []
        lines = content.split('\n')
        fixed_lines = []
        
        for line in lines:
            # Convert various heading formats to markdown
            if re.match(r'^([A-Z][A-Z\s]+)$', line.strip()) and len(line.strip()) < 50:
                # ALL CAPS to markdown heading
                fixed_line = f"## {line.strip().title()}"
                fixed_lines.append(fixed_line)
                changes.append(f"Fixed heading format: {line.strip()} -> {fixed_line}")
            elif re.match(r'^\d+\.?\s+([A-Z].+)$', line.strip()):
                # Numbered heading to markdown
                match = re.match(r'^\d+\.?\s+(.+)$', line.strip())
                if match:
                    fixed_line = f"## {match.group(1)}"
                    fixed_lines.append(fixed_line)
                    changes.append(f"Fixed numbered heading: {line.strip()} -> {fixed_line}")
            else:
                fixed_lines.append(line)
        
        return '\n'.join(fixed_lines), changes
    
    def _fix_paragraph_spacing(self, content: str) -> tuple[str, List[str]]:
        """Fix paragraph spacing"""
        changes = []
        
        # Remove excessive blank lines
        content = re.sub(r'\n{3,}', '\n\n', content)
        changes.append("Fixed excessive blank lines")
        
        # Ensure proper spacing after headings
        content = re.sub(r'(^#{1,6}\s+.+)(\n)([^\n])', r'\1\n\n\3', content, flags=re.MULTILINE)
        changes.append("Added proper spacing after headings")
        
        return content, changes
    
    def _fix_citations(self, content: str) -> tuple[str, List[str]]:
        """Fix basic citation formatting"""
        changes = []
        
        # Fix common citation patterns
        citation_patterns = [
            (r'\(([^)]+),\s*(\d{4})\)', r'(\1, \2)'),  # Fix spacing in citations
            (r'\[(\d+)\]', r'[\1]'),  # Ensure proper bracket citations
        ]
        
        for pattern, replacement in citation_patterns:
            if re.search(pattern, content):
                content = re.sub(pattern, replacement, content)
                changes.append(f"Fixed citation format: {pattern}")
        
        return content, changes
    
    def _fix_list_formatting(self, content: str) -> tuple[str, List[str]]:
        """Fix list formatting"""
        changes = []
        lines = content.split('\n')
        fixed_lines = []
        
        for line in lines:
            # Fix bullet points
            if re.match(r'^\s*[-*]\s*(.+)', line):
                fixed_line = re.sub(r'^\s*[-*]\s*', '- ', line)
                fixed_lines.append(fixed_line)
                if fixed_line != line:
                    changes.append("Fixed bullet point formatting")
            # Fix numbered lists
            elif re.match(r'^\s*\d+[.)]\s*(.+)', line):
                match = re.match(r'^\s*(\d+)[.)]\s*(.+)', line)
                if match:
                    fixed_line = f"{match.group(1)}. {match.group(2)}"
                    fixed_lines.append(fixed_line)
                    if fixed_line != line:
                        changes.append("Fixed numbered list formatting")
            else:
                fixed_lines.append(line)
        
        return '\n'.join(fixed_lines), changes
    
    def _calculate_compliance_score(self, original: str, formatted: str) -> float:
        """Calculate formatting compliance score"""
        score = 100.0
        
        # Check for proper headings
        headings = re.findall(r'^#{1,6}\s+', formatted, re.MULTILINE)
        if len(headings) < 3:
            score -= 20
        
        # Check for proper paragraph spacing
        excessive_spacing = len(re.findall(r'\n{3,}', formatted))
        score -= excessive_spacing * 5
        
        # Check for consistent list formatting
        inconsistent_bullets = len(re.findall(r'^\s*[*]\s+', formatted, re.MULTILINE))
        if inconsistent_bullets > 0:
            score -= 10
        
        return max(0.0, score)

class ContentBalancerTool(BaseTool):
    """Tool for balancing content distribution across sections"""
    name: str = "content_balancer"
    description: str = "Balance content distribution across document sections"
    
    def _run(self, content: str, sections: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Balance content across sections"""
        try:
            if not sections:
                sections = self._identify_sections_for_balancing(content)
            
            balanced_content, changes = self._balance_sections(content, sections)
            
            return {
                "balanced_content": balanced_content,
                "changes_made": changes,
                "balance_score": self._calculate_balance_score(sections)
            }
        except Exception as e:
            return {"error": f"Content balancing failed: {str(e)}"}
    
    def _identify_sections_for_balancing(self, content: str) -> List[Dict[str, Any]]:
        """Identify sections for content balancing"""
        sections = []
        current_section = {"content": "", "word_count": 0}
        
        for line in content.split('\n'):
            if re.match(r'^#{1,6}\s+', line):
                if current_section["content"]:
                    sections.append(current_section)
                current_section = {"title": line, "content": "", "word_count": 0}
            else:
                current_section["content"] += line + "\n"
                current_section["word_count"] = len(current_section["content"].split())
        
        if current_section["content"]:
            sections.append(current_section)
        
        return sections
    
    def _balance_sections(self, content: str, sections: List[Dict[str, Any]]) -> tuple[str, List[str]]:
        """Balance content across sections"""
        changes = []
        
        if len(sections) < 2:
            return content, ["Insufficient sections for balancing"]
        
        # Calculate statistics
        word_counts = [s["word_count"] for s in sections if s["word_count"] > 0]
        if not word_counts:
            return content, ["No content to balance"]
        
        avg_words = sum(word_counts) / len(word_counts)
        min_words = min(word_counts)
        max_words = max(word_counts)
        
        # Identify sections that need expansion
        short_sections = [s for s in sections if s["word_count"] < avg_words * 0.5]
        
        if short_sections:
            changes.append(f"Identified {len(short_sections)} sections needing expansion")
            
            # Add expansion suggestions to short sections
            for section in short_sections:
                if "title" in section:
                    expanded_content = self._expand_section_content(section)
                    section["content"] = expanded_content
                    changes.append(f"Expanded section: {section.get('title', 'Unknown')}")
        
        # Rebuild content
        balanced_parts = []
        for section in sections:
            if "title" in section:
                balanced_parts.append(section["title"])
                balanced_parts.append("")
            balanced_parts.append(section["content"].strip())
            balanced_parts.append("")
        
        balanced_content = '\n'.join(balanced_parts)
        
        return balanced_content, changes
    
    def _expand_section_content(self, section: Dict[str, Any]) -> str:
        """Expand section content with additional detail"""
        content = section["content"]
        
        # Add expansion based on section type
        if "implementation" in section.get("title", "").lower():
            expansion = "\n\nThe implementation process involved careful planning and systematic execution of the proposed solution. Technical challenges were addressed through iterative development and continuous testing."
        elif "result" in section.get("title", "").lower():
            expansion = "\n\nThe results demonstrate the effectiveness of the implemented solution. Performance metrics and user feedback indicate successful achievement of the project objectives."
        elif "method" in section.get("title", "").lower():
            expansion = "\n\nThe methodology was selected based on industry best practices and project requirements. This approach ensured systematic progress and measurable outcomes."
        else:
            expansion = "\n\nThis aspect of the project provided valuable learning opportunities and contributed significantly to the overall success of the internship experience."
        
        return content + expansion
    
    def _calculate_balance_score(self, sections: List[Dict[str, Any]]) -> float:
        """Calculate content balance score"""
        if len(sections) < 2:
            return 100.0
        
        word_counts = [s["word_count"] for s in sections if s["word_count"] > 0]
        if not word_counts:
            return 0.0
        
        avg_words = sum(word_counts) / len(word_counts)
        variance = sum((count - avg_words) ** 2 for count in word_counts) / len(word_counts)
        coefficient_of_variation = (variance ** 0.5) / avg_words if avg_words > 0 else 0
        
        # Lower coefficient of variation = better balance
        balance_score = max(0, 100 - (coefficient_of_variation * 100))
        return balance_score

class HierarchyOptimizerTool(BaseTool):
    """Tool for optimizing heading hierarchy and section numbering"""
    name: str = "hierarchy_optimizer"
    description: str = "Optimize heading hierarchy and section numbering"
    
    def _run(self, content: str) -> Dict[str, Any]:
        """Optimize heading hierarchy"""
        try:
            optimized_content, changes = self._optimize_hierarchy(content)
            
            return {
                "optimized_content": optimized_content,
                "changes_made": changes,
                "hierarchy_score": self._calculate_hierarchy_score(optimized_content)
            }
        except Exception as e:
            return {"error": f"Hierarchy optimization failed: {str(e)}"}
    
    def _optimize_hierarchy(self, content: str) -> tuple[str, List[str]]:
        """Optimize heading hierarchy"""
        changes = []
        lines = content.split('\n')
        optimized_lines = []
        
        section_counter = 1
        subsection_counter = 1
        
        for line in lines:
            if re.match(r'^#{1,6}\s+', line):
                # Determine heading level
                level = len(re.match(r'^(#+)', line).group(1))
                title = re.sub(r'^#+\s+', '', line)
                
                if level <= 2:  # Main section
                    optimized_line = f"## {section_counter}. {title}"
                    section_counter += 1
                    subsection_counter = 1
                else:  # Subsection
                    optimized_line = f"### {section_counter-1}.{subsection_counter} {title}"
                    subsection_counter += 1
                
                optimized_lines.append(optimized_line)
                changes.append(f"Optimized heading: {line} -> {optimized_line}")
            else:
                optimized_lines.append(line)
        
        return '\n'.join(optimized_lines), changes
    
    def _calculate_hierarchy_score(self, content: str) -> float:
        """Calculate hierarchy optimization score"""
        headings = re.findall(r'^(#+)\s+(.+)', content, re.MULTILINE)
        
        if not headings:
            return 0.0
        
        # Check for consistent hierarchy
        levels = [len(h[0]) for h in headings]
        
        # Score based on proper hierarchy progression
        score = 100.0
        for i in range(1, len(levels)):
            if levels[i] > levels[i-1] + 1:
                score -= 10  # Penalty for skipping levels
        
        return max(0.0, score)

class StructureOptimizer:
    """Structure Optimization Agent - main class"""
    
    def __init__(self):
        """Initialize structure optimizer with tools"""
        self.tools = [
            StructureReorganizerTool(),
            FormatValidatorTool(),
            ContentBalancerTool(),
            HierarchyOptimizerTool()
        ]
    
    def get_tools(self) -> List[BaseTool]:
        """Get list of available tools"""
        return self.tools
    
    def optimize_structure(self, content: str, focus_areas: List[str] = None) -> Dict[str, Any]:
        """Main method to optimize document structure"""
        try:
            if not focus_areas:
                focus_areas = ['structure_organization', 'formatting']
            
            optimized_content = content
            all_changes = []
            
            # Step 1: Reorganize structure
            if 'structure_organization' in focus_areas:
                reorganizer_tool = self.tools[0]
                reorg_result = reorganizer_tool._run(optimized_content)
                
                if "error" not in reorg_result:
                    optimized_content = reorg_result["reorganized_content"]
                    all_changes.extend(reorg_result["changes_made"])
            
            # Step 2: Format validation
            if 'formatting' in focus_areas:
                format_tool = self.tools[1]
                format_result = format_tool._run(optimized_content)
                
                if "error" not in format_result:
                    optimized_content = format_result["formatted_content"]
                    all_changes.extend(format_result["changes_made"])
            
            # Step 3: Content balancing
            balancer_tool = self.tools[2]
            balance_result = balancer_tool._run(optimized_content)
            
            if "error" not in balance_result:
                optimized_content = balance_result["balanced_content"]
                all_changes.extend(balance_result["changes_made"])
            
            # Step 4: Hierarchy optimization
            hierarchy_tool = self.tools[3]
            hierarchy_result = hierarchy_tool._run(optimized_content)
            
            if "error" not in hierarchy_result:
                optimized_content = hierarchy_result["optimized_content"]
                all_changes.extend(hierarchy_result["changes_made"])
            
            # Compile final results
            final_result = {
                "optimized_content": optimized_content,
                "original_content": content,
                "changes_made": all_changes,
                "focus_areas": focus_areas,
                "structure_score": reorg_result.get("structure_score", 0) if 'reorg_result' in locals() else 0,
                "compliance_score": format_result.get("compliance_score", 0) if 'format_result' in locals() else 0,
                "balance_score": balance_result.get("balance_score", 0) if 'balance_result' in locals() else 0,
                "hierarchy_score": hierarchy_result.get("hierarchy_score", 0) if 'hierarchy_result' in locals() else 0
            }
            
            return final_result
            
        except Exception as e:
            return {"error": f"Structure optimization failed: {str(e)}"}
