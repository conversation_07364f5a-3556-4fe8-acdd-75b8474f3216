[global]
# Global configuration for the Streamlit app

# Development mode
developmentMode = false

# Show error details in production
showErrorDetails = false

# Data frame serialization
dataFrameSerialization = "legacy"

[server]
# Server configuration
port = 8501
baseUrlPath = ""
enableCORS = false
enableXsrfProtection = true
maxUploadSize = 50
maxMessageSize = 50
enableWebsocketCompression = false

# Session state
runOnSave = false
allowRunOnSave = false

# File watcher
fileWatcherType = "auto"

[browser]
# Browser configuration
serverAddress = "localhost"
gatherUsageStats = false
serverPort = 8501

[theme]
# Theme configuration
primaryColor = "#FF6B6B"
backgroundColor = "#FFFFFF"
secondaryBackgroundColor = "#F0F2F6"
textColor = "#262730"
font = "sans serif"

[client]
# Client configuration
caching = true
displayEnabled = true
showErrorDetails = false

[runner]
# Runner configuration
magicEnabled = true
installTracer = false
fixMatplotlib = true
postScriptGC = true
fastReruns = true
enforceSerializableSessionState = true

[logger]
# Logging configuration
level = "info"
messageFormat = "%(asctime)s %(message)s"

[deprecation]
# Deprecation warnings
showPyplotGlobalUse = false
