# Streamlit Secrets Configuration
# This file should be configured in Streamlit Cloud dashboard
# DO NOT commit this file with real secrets to version control

[general]
# GroqCloud API
GROQ_API_KEY = "your_groq_api_key_here"

# Google OAuth
GOOGLE_CLIENT_ID = "your_google_client_id.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET = "your_google_client_secret"

# Security
COOKIE_KEY = "your_secure_random_cookie_key_32_chars"
SECRET_KEY = "your_secret_key_for_encryption"

# Email Configuration
SMTP_SERVER = "smtp.gmail.com"
SMTP_PORT = 587
EMAIL_USER = "<EMAIL>"
EMAIL_PASSWORD = "your_app_password"

# Application Settings
APP_NAME = "Multi-Agent Internship Report Generator"
DEBUG = false
ENVIRONMENT = "production"

[database]
# Database configuration
DATABASE_URL = "sqlite:///./internship_reports.db"

[ai]
# AI Model Configuration
DEFAULT_MODEL = "llama3-70b-8192"
TEMPERATURE = 0.7
MAX_TOKENS = 4096

[features]
# Feature flags
ENABLE_EMAIL_DELIVERY = true
ENABLE_VISUAL_GENERATION = true
ENABLE_OCR = true
ENABLE_ANALYTICS = true
