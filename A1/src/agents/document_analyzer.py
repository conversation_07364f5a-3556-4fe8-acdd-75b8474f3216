"""
Document Analysis Agent
Specialized agent for analyzing document structure and content quality
"""

import os
import json
import re
from typing import Dict, Any, List, Optional
from pathlib import Path
import PyPDF2
import docx
from PIL import Image
import pytesseract
import streamlit as st

from crewai.tools import BaseTool
from pydantic import BaseModel, Field

class DocumentParserTool(BaseTool):
    """Tool for parsing various document formats"""
    name: str = "document_parser"
    description: str = "Parse documents in PDF, DOCX, TXT, and image formats to extract text content"
    
    def _run(self, file_path: str) -> Dict[str, Any]:
        """Parse document and extract text content"""
        try:
            file_path = Path(file_path)
            file_extension = file_path.suffix.lower()
            
            if file_extension == '.pdf':
                return self._parse_pdf(file_path)
            elif file_extension == '.docx':
                return self._parse_docx(file_path)
            elif file_extension == '.txt':
                return self._parse_txt(file_path)
            elif file_extension in ['.png', '.jpg', '.jpeg', '.tiff', '.bmp']:
                return self._parse_image(file_path)
            else:
                return {"error": f"Unsupported file format: {file_extension}"}
                
        except Exception as e:
            return {"error": f"Error parsing document: {str(e)}"}
    
    def _parse_pdf(self, file_path: Path) -> Dict[str, Any]:
        """Parse PDF document"""
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text_content = ""
                page_count = len(pdf_reader.pages)
                
                for page_num, page in enumerate(pdf_reader.pages):
                    text_content += f"\n--- Page {page_num + 1} ---\n"
                    text_content += page.extract_text()
                
                return {
                    "content": text_content,
                    "page_count": page_count,
                    "file_type": "pdf",
                    "word_count": len(text_content.split()),
                    "character_count": len(text_content)
                }
        except Exception as e:
            return {"error": f"Error parsing PDF: {str(e)}"}
    
    def _parse_docx(self, file_path: Path) -> Dict[str, Any]:
        """Parse DOCX document"""
        try:
            doc = docx.Document(file_path)
            text_content = ""
            paragraph_count = 0
            
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content += paragraph.text + "\n"
                    paragraph_count += 1
            
            return {
                "content": text_content,
                "paragraph_count": paragraph_count,
                "file_type": "docx",
                "word_count": len(text_content.split()),
                "character_count": len(text_content)
            }
        except Exception as e:
            return {"error": f"Error parsing DOCX: {str(e)}"}
    
    def _parse_txt(self, file_path: Path) -> Dict[str, Any]:
        """Parse TXT document"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                text_content = file.read()
            
            return {
                "content": text_content,
                "line_count": len(text_content.split('\n')),
                "file_type": "txt",
                "word_count": len(text_content.split()),
                "character_count": len(text_content)
            }
        except Exception as e:
            return {"error": f"Error parsing TXT: {str(e)}"}
    
    def _parse_image(self, file_path: Path) -> Dict[str, Any]:
        """Parse image using OCR"""
        try:
            image = Image.open(file_path)
            text_content = pytesseract.image_to_string(image)
            
            return {
                "content": text_content,
                "file_type": "image",
                "word_count": len(text_content.split()),
                "character_count": len(text_content),
                "image_size": image.size,
                "ocr_confidence": "medium"  # Could be enhanced with actual confidence scores
            }
        except Exception as e:
            return {"error": f"Error parsing image with OCR: {str(e)}"}

class ContentExtractorTool(BaseTool):
    """Tool for extracting and categorizing content sections"""
    name: str = "content_extractor"
    description: str = "Extract and categorize different sections of the document content"
    
    def _run(self, content: str) -> Dict[str, Any]:
        """Extract and categorize content sections"""
        try:
            sections = self._identify_sections(content)
            categories = self._categorize_content(content)
            
            return {
                "sections": sections,
                "categories": categories,
                "total_sections": len(sections),
                "content_distribution": self._analyze_content_distribution(sections)
            }
        except Exception as e:
            return {"error": f"Error extracting content: {str(e)}"}
    
    def _identify_sections(self, content: str) -> List[Dict[str, Any]]:
        """Identify document sections based on headings and structure"""
        sections = []
        lines = content.split('\n')
        current_section = None
        
        # Common heading patterns
        heading_patterns = [
            r'^#{1,6}\s+(.+)$',  # Markdown headings
            r'^([A-Z][A-Z\s]+)$',  # ALL CAPS headings
            r'^\d+\.?\s+([A-Z].+)$',  # Numbered headings
            r'^([A-Z][a-z\s]+):?\s*$'  # Title case headings
        ]
        
        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
                
            # Check if line matches heading patterns
            is_heading = False
            heading_text = ""
            
            for pattern in heading_patterns:
                match = re.match(pattern, line)
                if match:
                    is_heading = True
                    heading_text = match.group(1) if match.groups() else line
                    break
            
            if is_heading:
                # Save previous section
                if current_section:
                    sections.append(current_section)
                
                # Start new section
                current_section = {
                    "title": heading_text,
                    "start_line": i,
                    "content": "",
                    "word_count": 0
                }
            elif current_section:
                current_section["content"] += line + "\n"
                current_section["word_count"] = len(current_section["content"].split())
        
        # Add last section
        if current_section:
            sections.append(current_section)
        
        return sections
    
    def _categorize_content(self, content: str) -> Dict[str, List[str]]:
        """Categorize content into different types"""
        categories = {
            "technical_terms": [],
            "code_snippets": [],
            "urls_references": [],
            "dates": [],
            "technologies": []
        }
        
        # Technical terms (simplified detection)
        tech_patterns = [
            r'\b(API|SDK|HTTP|JSON|XML|SQL|HTML|CSS|JavaScript|Python|Java|React|Angular|Vue)\b',
            r'\b(database|server|client|framework|library|algorithm|function|method|class)\b',
            r'\b(frontend|backend|fullstack|DevOps|CI/CD|deployment|testing|debugging)\b'
        ]
        
        for pattern in tech_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            categories["technical_terms"].extend(matches)
        
        # Code snippets
        code_patterns = [
            r'```[\s\S]*?```',  # Markdown code blocks
            r'`[^`]+`',  # Inline code
            r'\b\w+\(\)',  # Function calls
        ]
        
        for pattern in code_patterns:
            matches = re.findall(pattern, content)
            categories["code_snippets"].extend(matches)
        
        # URLs and references
        url_pattern = r'https?://[^\s]+'
        categories["urls_references"] = re.findall(url_pattern, content)
        
        # Dates
        date_patterns = [
            r'\b\d{1,2}/\d{1,2}/\d{4}\b',
            r'\b\d{4}-\d{2}-\d{2}\b',
            r'\b(January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},?\s+\d{4}\b'
        ]
        
        for pattern in date_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            categories["dates"].extend(matches)
        
        return categories
    
    def _analyze_content_distribution(self, sections: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze content distribution across sections"""
        if not sections:
            return {"error": "No sections found"}
        
        total_words = sum(section["word_count"] for section in sections)
        
        distribution = {
            "total_words": total_words,
            "average_section_length": total_words / len(sections) if sections else 0,
            "longest_section": max(sections, key=lambda x: x["word_count"])["title"] if sections else None,
            "shortest_section": min(sections, key=lambda x: x["word_count"])["title"] if sections else None,
            "section_balance": "balanced" if max(s["word_count"] for s in sections) / min(s["word_count"] for s in sections) < 3 else "unbalanced"
        }
        
        return distribution

class StructureAnalyzerTool(BaseTool):
    """Tool for analyzing document structure and organization"""
    name: str = "structure_analyzer"
    description: str = "Analyze document structure, heading hierarchy, and organization"
    
    def _run(self, content: str, sections: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze document structure"""
        try:
            structure_analysis = {
                "heading_hierarchy": self._analyze_heading_hierarchy(content),
                "section_flow": self._analyze_section_flow(sections),
                "organization_score": self._calculate_organization_score(sections),
                "structural_issues": self._identify_structural_issues(sections),
                "recommendations": self._generate_structure_recommendations(sections)
            }
            
            return structure_analysis
        except Exception as e:
            return {"error": f"Error analyzing structure: {str(e)}"}
    
    def _analyze_heading_hierarchy(self, content: str) -> Dict[str, Any]:
        """Analyze heading hierarchy and consistency"""
        lines = content.split('\n')
        headings = []
        
        for line in lines:
            line = line.strip()
            # Check for markdown headings
            if re.match(r'^#{1,6}\s+', line):
                level = len(re.match(r'^(#+)', line).group(1))
                text = re.sub(r'^#+\s+', '', line)
                headings.append({"level": level, "text": text, "type": "markdown"})
        
        return {
            "total_headings": len(headings),
            "max_depth": max([h["level"] for h in headings]) if headings else 0,
            "hierarchy_consistency": self._check_hierarchy_consistency(headings),
            "headings": headings
        }
    
    def _check_hierarchy_consistency(self, headings: List[Dict[str, Any]]) -> str:
        """Check if heading hierarchy is consistent"""
        if not headings:
            return "no_headings"
        
        levels = [h["level"] for h in headings]
        
        # Check for proper progression
        for i in range(1, len(levels)):
            if levels[i] > levels[i-1] + 1:
                return "inconsistent"
        
        return "consistent"
    
    def _analyze_section_flow(self, sections: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze logical flow between sections"""
        if len(sections) < 2:
            return {"flow_quality": "insufficient_sections"}
        
        # Common academic report structure
        expected_sections = [
            "introduction", "background", "methodology", "implementation", 
            "results", "discussion", "conclusion", "references"
        ]
        
        found_sections = []
        for section in sections:
            title_lower = section["title"].lower()
            for expected in expected_sections:
                if expected in title_lower:
                    found_sections.append(expected)
                    break
        
        return {
            "expected_sections_found": len(found_sections),
            "total_expected": len(expected_sections),
            "missing_sections": [s for s in expected_sections if s not in found_sections],
            "flow_quality": "good" if len(found_sections) >= 5 else "needs_improvement"
        }
    
    def _calculate_organization_score(self, sections: List[Dict[str, Any]]) -> float:
        """Calculate overall organization score (0-100)"""
        if not sections:
            return 0.0
        
        score = 0.0
        
        # Section count score (20 points)
        if 5 <= len(sections) <= 10:
            score += 20
        elif 3 <= len(sections) <= 12:
            score += 15
        else:
            score += 10
        
        # Content balance score (30 points)
        word_counts = [s["word_count"] for s in sections]
        if word_counts:
            avg_words = sum(word_counts) / len(word_counts)
            balance_score = 30 - min(30, abs(max(word_counts) - min(word_counts)) / avg_words * 10)
            score += balance_score
        
        # Title quality score (25 points)
        title_score = 0
        for section in sections:
            title = section["title"]
            if len(title.split()) >= 2:  # Multi-word titles
                title_score += 5
            if title[0].isupper():  # Proper capitalization
                title_score += 3
        score += min(25, title_score)
        
        # Logical progression score (25 points)
        # Simplified: check if sections have reasonable word counts
        if all(s["word_count"] > 50 for s in sections):
            score += 25
        else:
            score += 15
        
        return min(100.0, score)
    
    def _identify_structural_issues(self, sections: List[Dict[str, Any]]) -> List[str]:
        """Identify structural issues in the document"""
        issues = []
        
        if len(sections) < 3:
            issues.append("Too few sections - document may lack proper structure")
        
        if len(sections) > 15:
            issues.append("Too many sections - consider consolidating related content")
        
        # Check for very short sections
        short_sections = [s for s in sections if s["word_count"] < 50]
        if short_sections:
            issues.append(f"{len(short_sections)} sections are too short (< 50 words)")
        
        # Check for very long sections
        long_sections = [s for s in sections if s["word_count"] > 1000]
        if long_sections:
            issues.append(f"{len(long_sections)} sections are very long (> 1000 words) - consider breaking them down")
        
        return issues
    
    def _generate_structure_recommendations(self, sections: List[Dict[str, Any]]) -> List[str]:
        """Generate recommendations for improving document structure"""
        recommendations = []
        
        if len(sections) < 5:
            recommendations.append("Consider adding more sections to improve document organization")
        
        # Check for missing common sections
        section_titles = [s["title"].lower() for s in sections]
        
        if not any("introduction" in title for title in section_titles):
            recommendations.append("Add an introduction section to provide context")
        
        if not any("conclusion" in title for title in section_titles):
            recommendations.append("Add a conclusion section to summarize key points")
        
        if not any("method" in title or "approach" in title for title in section_titles):
            recommendations.append("Consider adding a methodology section to explain your approach")
        
        return recommendations

class QualityAssessorTool(BaseTool):
    """Tool for assessing content quality and writing style"""
    name: str = "quality_assessor"
    description: str = "Assess content quality, writing style, and technical accuracy"
    
    def _run(self, content: str) -> Dict[str, Any]:
        """Assess overall content quality"""
        try:
            quality_assessment = {
                "writing_quality": self._assess_writing_quality(content),
                "technical_depth": self._assess_technical_depth(content),
                "clarity_score": self._assess_clarity(content),
                "professionalism_score": self._assess_professionalism(content),
                "overall_score": 0,
                "improvement_areas": []
            }
            
            # Calculate overall score
            scores = [
                quality_assessment["writing_quality"]["score"],
                quality_assessment["technical_depth"]["score"],
                quality_assessment["clarity_score"],
                quality_assessment["professionalism_score"]
            ]
            quality_assessment["overall_score"] = sum(scores) / len(scores)
            
            # Generate improvement areas
            quality_assessment["improvement_areas"] = self._identify_improvement_areas(quality_assessment)
            
            return quality_assessment
        except Exception as e:
            return {"error": f"Error assessing quality: {str(e)}"}
    
    def _assess_writing_quality(self, content: str) -> Dict[str, Any]:
        """Assess writing quality metrics"""
        sentences = re.split(r'[.!?]+', content)
        sentences = [s.strip() for s in sentences if s.strip()]
        
        words = content.split()
        
        # Calculate metrics
        avg_sentence_length = len(words) / len(sentences) if sentences else 0
        
        # Simple readability assessment
        readability_score = 100 - (avg_sentence_length * 1.5)
        readability_score = max(0, min(100, readability_score))
        
        return {
            "score": readability_score,
            "average_sentence_length": avg_sentence_length,
            "total_sentences": len(sentences),
            "readability": "good" if readability_score > 70 else "needs_improvement"
        }
    
    def _assess_technical_depth(self, content: str) -> Dict[str, Any]:
        """Assess technical depth and accuracy"""
        technical_indicators = [
            r'\b(implement|develop|design|architect|optimize|debug|test|deploy)\b',
            r'\b(algorithm|function|method|class|object|variable|parameter)\b',
            r'\b(database|server|client|API|framework|library|module)\b',
            r'\b(performance|scalability|security|reliability|maintainability)\b'
        ]
        
        technical_count = 0
        for pattern in technical_indicators:
            matches = re.findall(pattern, content, re.IGNORECASE)
            technical_count += len(matches)
        
        word_count = len(content.split())
        technical_density = (technical_count / word_count * 100) if word_count > 0 else 0
        
        score = min(100, technical_density * 10)  # Scale to 0-100
        
        return {
            "score": score,
            "technical_terms_count": technical_count,
            "technical_density": technical_density,
            "depth_level": "high" if score > 70 else "medium" if score > 40 else "low"
        }
    
    def _assess_clarity(self, content: str) -> float:
        """Assess content clarity"""
        # Simple clarity metrics
        words = content.split()
        
        # Check for passive voice (simplified)
        passive_indicators = re.findall(r'\b(was|were|been|being)\s+\w+ed\b', content, re.IGNORECASE)
        passive_ratio = len(passive_indicators) / len(words) * 100 if words else 0
        
        # Check for complex sentences (simplified)
        complex_sentences = re.findall(r'[,;:]{2,}', content)
        complexity_ratio = len(complex_sentences) / len(words) * 100 if words else 0
        
        # Calculate clarity score (higher is better)
        clarity_score = 100 - (passive_ratio * 2) - (complexity_ratio * 5)
        return max(0, min(100, clarity_score))
    
    def _assess_professionalism(self, content: str) -> float:
        """Assess professionalism of writing"""
        # Check for informal language
        informal_words = [
            'gonna', 'wanna', 'kinda', 'sorta', 'yeah', 'ok', 'okay',
            'stuff', 'things', 'lots of', 'a lot of'
        ]
        
        informal_count = 0
        for word in informal_words:
            informal_count += len(re.findall(r'\b' + word + r'\b', content, re.IGNORECASE))
        
        word_count = len(content.split())
        informal_ratio = (informal_count / word_count * 100) if word_count > 0 else 0
        
        # Check for proper capitalization
        sentences = re.split(r'[.!?]+', content)
        proper_caps = sum(1 for s in sentences if s.strip() and s.strip()[0].isupper())
        cap_ratio = (proper_caps / len(sentences) * 100) if sentences else 0
        
        # Calculate professionalism score
        professionalism_score = cap_ratio - (informal_ratio * 10)
        return max(0, min(100, professionalism_score))
    
    def _identify_improvement_areas(self, assessment: Dict[str, Any]) -> List[str]:
        """Identify specific areas for improvement"""
        areas = []
        
        if assessment["writing_quality"]["score"] < 70:
            areas.append("Improve sentence structure and readability")
        
        if assessment["technical_depth"]["score"] < 50:
            areas.append("Add more technical details and depth")
        
        if assessment["clarity_score"] < 70:
            areas.append("Improve clarity and reduce complexity")
        
        if assessment["professionalism_score"] < 80:
            areas.append("Enhance professional tone and language")
        
        return areas

class DocumentAnalyzer:
    """Document Analysis Agent - main class"""
    
    def __init__(self):
        """Initialize document analyzer with tools"""
        self.tools = [
            DocumentParserTool(),
            ContentExtractorTool(),
            StructureAnalyzerTool(),
            QualityAssessorTool()
        ]
    
    def get_tools(self) -> List[BaseTool]:
        """Get list of available tools"""
        return self.tools
    
    def analyze_document(self, file_path: str) -> Dict[str, Any]:
        """Main method to analyze a document"""
        try:
            # Parse document
            parser_tool = self.tools[0]
            parse_result = parser_tool._run(file_path)
            
            if "error" in parse_result:
                return parse_result
            
            content = parse_result["content"]
            
            # Extract content sections
            extractor_tool = self.tools[1]
            extraction_result = extractor_tool._run(content)
            
            # Analyze structure
            structure_tool = self.tools[2]
            structure_result = structure_tool._run(content, extraction_result.get("sections", []))
            
            # Assess quality
            quality_tool = self.tools[3]
            quality_result = quality_tool._run(content)
            
            # Compile comprehensive analysis
            analysis_result = {
                "document_info": parse_result,
                "content_analysis": extraction_result,
                "structure_analysis": structure_result,
                "quality_assessment": quality_result,
                "overall_recommendations": self._generate_overall_recommendations(
                    structure_result, quality_result
                )
            }
            
            return analysis_result
            
        except Exception as e:
            return {"error": f"Document analysis failed: {str(e)}"}
    
    def _generate_overall_recommendations(self, structure_result: Dict[str, Any], 
                                        quality_result: Dict[str, Any]) -> List[str]:
        """Generate overall recommendations based on analysis"""
        recommendations = []
        
        # Structure recommendations
        if "recommendations" in structure_result:
            recommendations.extend(structure_result["recommendations"])
        
        # Quality recommendations
        if "improvement_areas" in quality_result:
            recommendations.extend(quality_result["improvement_areas"])
        
        # Overall score-based recommendations
        if quality_result.get("overall_score", 0) < 60:
            recommendations.append("Consider comprehensive rewriting for better quality")
        elif quality_result.get("overall_score", 0) < 80:
            recommendations.append("Focus on targeted improvements in identified areas")
        
        return list(set(recommendations))  # Remove duplicates
