"""
Google OAuth Authentication Module
Handles user authentication using Google OAuth 2.0
"""

import streamlit as st
import os
from typing import Optional, Dict, Any
import json
import requests
from urllib.parse import urlencode
import secrets
import hashlib
import base64

class GoogleAuthenticator:
    """Google OAuth 2.0 authentication handler"""

    def __init__(self):
        """Initialize Google authenticator with configuration"""
        self.client_id = self._get_config("GOOGLE_CLIENT_ID")
        self.client_secret = self._get_config("GOOGLE_CLIENT_SECRET")
        self.redirect_uri = self._get_config("GOOGLE_REDIRECT_URI", "http://localhost:8501")

        # OAuth endpoints
        self.auth_url = "https://accounts.google.com/o/oauth2/auth"
        self.token_url = "https://oauth2.googleapis.com/token"
        self.userinfo_url = "https://www.googleapis.com/oauth2/v2/userinfo"

        # Scopes
        self.scopes = [
            "openid",
            "email",
            "profile"
        ]

    def _get_config(self, key: str, default: str = None) -> str:
        """Get configuration value from environment or Streamlit secrets"""
        # Try Streamlit secrets first
        try:
            return st.secrets["general"][key]
        except (KeyError, FileNotFoundError):
            pass

        # Try environment variables
        value = os.getenv(key, default)
        if not value and default is None:
            # For demo purposes, return demo values
            demo_values = {
                'GOOGLE_CLIENT_ID': 'demo_client_id',
                'GOOGLE_CLIENT_SECRET': 'demo_client_secret',
                'GOOGLE_REDIRECT_URI': 'http://localhost:8501'
            }
            return demo_values.get(key, '')

        return value or ''

    def _generate_state(self) -> str:
        """Generate a secure random state parameter"""
        return secrets.token_urlsafe(32)

    def _generate_code_verifier(self) -> str:
        """Generate code verifier for PKCE"""
        return base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')

    def _generate_code_challenge(self, verifier: str) -> str:
        """Generate code challenge from verifier"""
        digest = hashlib.sha256(verifier.encode('utf-8')).digest()
        return base64.urlsafe_b64encode(digest).decode('utf-8').rstrip('=')

    def get_authorization_url(self) -> tuple[str, str, str]:
        """Generate authorization URL for OAuth flow"""
        state = self._generate_state()
        code_verifier = self._generate_code_verifier()
        code_challenge = self._generate_code_challenge(code_verifier)

        # Store state and code verifier in session
        st.session_state.oauth_state = state
        st.session_state.code_verifier = code_verifier

        params = {
            "client_id": self.client_id,
            "redirect_uri": self.redirect_uri,
            "scope": " ".join(self.scopes),
            "response_type": "code",
            "state": state,
            "code_challenge": code_challenge,
            "code_challenge_method": "S256",
            "access_type": "offline",
            "prompt": "consent"
        }

        auth_url = f"{self.auth_url}?{urlencode(params)}"
        return auth_url, state, code_verifier

    def exchange_code_for_token(self, code: str, state: str) -> Optional[Dict[str, Any]]:
        """Exchange authorization code for access token"""
        # Verify state parameter
        if state != st.session_state.get('oauth_state'):
            raise ValueError("Invalid state parameter")

        code_verifier = st.session_state.get('code_verifier')
        if not code_verifier:
            raise ValueError("Code verifier not found")

        data = {
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "code": code,
            "grant_type": "authorization_code",
            "redirect_uri": self.redirect_uri,
            "code_verifier": code_verifier
        }

        try:
            response = requests.post(self.token_url, data=data)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            st.error(f"Token exchange failed: {str(e)}")
            return None

    def get_user_info(self, access_token: str) -> Optional[Dict[str, Any]]:
        """Get user information using access token"""
        headers = {"Authorization": f"Bearer {access_token}"}

        try:
            response = requests.get(self.userinfo_url, headers=headers)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            st.error(f"Failed to get user info: {str(e)}")
            return None

    def authenticate(self) -> Optional[Dict[str, Any]]:
        """Main authentication flow"""
        # Check for authorization code in URL parameters
        query_params = st.experimental_get_query_params()

        if "code" in query_params and "state" in query_params:
            code = query_params["code"][0]
            state = query_params["state"][0]

            # Exchange code for token
            token_data = self.exchange_code_for_token(code, state)
            if token_data and "access_token" in token_data:
                # Get user information
                user_info = self.get_user_info(token_data["access_token"])
                if user_info:
                    # Store tokens in session
                    st.session_state.access_token = token_data["access_token"]
                    if "refresh_token" in token_data:
                        st.session_state.refresh_token = token_data["refresh_token"]

                    # Clear URL parameters
                    st.experimental_set_query_params()

                    return user_info

        # If no code in URL, redirect to authorization
        auth_url, state, code_verifier = self.get_authorization_url()

        # Check if we have valid OAuth credentials
        if self.client_id == 'demo_client_id':
            # Demo mode - simulate authentication
            st.warning("⚠️ Demo Mode: OAuth credentials not configured")
            if st.button("🚀 Continue with Demo Authentication"):
                # Simulate successful authentication
                demo_user = {
                    'id': 'demo_user_123',
                    'email': '<EMAIL>',
                    'name': 'Demo User',
                    'picture': 'https://via.placeholder.com/100'
                }

                st.session_state.authenticated = True
                st.session_state.user_info = demo_user
                st.session_state.access_token = 'demo_token'

                st.success("Demo authentication successful!")
                st.rerun()

                return demo_user
        else:
            # Real OAuth flow
            # Display authorization link
            st.markdown(f"""
            <div style="text-align: center; padding: 20px;">
                <a href="{auth_url}" target="_self" style="
                    background-color: #4285f4;
                    color: white;
                    padding: 12px 24px;
                    text-decoration: none;
                    border-radius: 6px;
                    font-weight: bold;
                    display: inline-block;
                ">
                    🔑 Sign in with Google
                </a>
            </div>
            """, unsafe_allow_html=True)

        return None

    def logout(self):
        """Logout user and clear session"""
        # Clear session state
        keys_to_clear = [
            'access_token', 'refresh_token', 'oauth_state',
            'code_verifier', 'authenticated', 'user_info'
        ]

        for key in keys_to_clear:
            if key in st.session_state:
                del st.session_state[key]

        st.success("Logged out successfully!")

    def is_authenticated(self) -> bool:
        """Check if user is currently authenticated"""
        return (
            st.session_state.get('authenticated', False) and
            st.session_state.get('access_token') is not None
        )

    def get_current_user(self) -> Optional[Dict[str, Any]]:
        """Get current authenticated user information"""
        if self.is_authenticated():
            return st.session_state.get('user_info')
        return None
