# Core Framework
streamlit>=1.28.0
crewai>=0.28.8
langchain>=0.1.0
langchain-community>=0.0.20

# AI and ML
groq>=0.4.1
openai>=1.12.0
langchain-openai>=0.0.8
sentence-transformers>=2.2.2
chromadb>=0.4.22

# Authentication
streamlit-google-auth>=0.0.204
google-auth>=2.17.0
google-auth-oauthlib>=1.0.0
google-auth-httplib2>=0.2.0

# Document Processing
PyPDF2>=3.0.1
pdfplumber>=0.9.0
python-docx>=0.8.11
reportlab>=4.0.4
Pillow>=10.0.0
pytesseract>=0.3.10
pdf2image>=1.16.3

# Database
sqlite3
sqlalchemy>=2.0.0
alembic>=1.13.0

# Email
smtplib
email-validator>=2.1.0
jinja2>=3.1.2

# Web and HTTP
requests>=2.31.0
httpx>=0.25.0
aiohttp>=3.9.0

# Data Processing
pandas>=2.1.0
numpy>=1.24.0
python-multipart>=0.0.6

# File Handling
python-magic>=0.4.27
chardet>=5.2.0

# Security
cryptography>=41.0.0
bcrypt>=4.1.0
python-jose>=3.3.0

# Environment and Configuration
python-dotenv>=1.0.0
pydantic>=2.5.0
pydantic-settings>=2.1.0

# Utilities
click>=8.1.0
rich>=13.7.0
tqdm>=4.66.0
python-dateutil>=2.8.2

# Development and Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
black>=23.12.0
flake8>=6.1.0
mypy>=1.8.0

# Monitoring and Logging
loguru>=0.7.2
sentry-sdk>=1.40.0

# Additional Streamlit Components
streamlit-option-menu>=0.3.6
streamlit-aggrid>=0.3.4
streamlit-lottie>=0.0.5
streamlit-elements>=0.1.0

# Image Processing
opencv-python>=4.8.0
matplotlib>=3.8.0
seaborn>=0.13.0

# API and Web Services
fastapi>=0.104.0
uvicorn>=0.24.0

# Async Support
asyncio
aiofiles>=23.2.1

# Data Validation
marshmallow>=3.20.0
cerberus>=1.3.5

# Caching
redis>=5.0.1
diskcache>=5.6.3

# File Format Support
openpyxl>=3.1.2
xlsxwriter>=3.1.9
markdown>=3.5.1

# Network and HTTP
urllib3>=2.1.0
certifi>=2023.11.17

# Time and Date
pytz>=2023.3
arrow>=1.3.0

# Configuration Management
configparser>=6.0.0
toml>=0.10.2
yaml>=0.2.5
pyyaml>=6.0.1

# Process Management
psutil>=5.9.6

# Compression
zipfile36>=0.1.3
tarfile>=0.1.0

# Text Processing
nltk>=3.8.1
spacy>=3.7.2
textblob>=0.17.1

# Web Scraping (if needed)
beautifulsoup4>=4.12.2
lxml>=4.9.3

# API Documentation
swagger-ui-bundle>=0.0.9

# Performance
cachetools>=5.3.2
memory-profiler>=0.61.0

# Deployment
gunicorn>=21.2.0
whitenoise>=6.6.0

# Cloud Storage (optional)
boto3>=1.34.0
google-cloud-storage>=2.10.0

# Additional Security
passlib>=1.7.4
itsdangerous>=2.1.2
