"""
Setup script for Multi-Agent Internship Report Generator
"""

from setuptools import setup, find_packages
import os

# Read README file
def read_readme():
    with open("README.md", "r", encoding="utf-8") as fh:
        return fh.read()

# Read requirements
def read_requirements():
    with open("requirements.txt", "r", encoding="utf-8") as fh:
        return [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="internship-report-generator",
    version="1.0.0",
    author="Multi-Agent Report Generator Team",
    author_email="<EMAIL>",
    description="A production-ready AI-powered system for creating and enhancing internship reports",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/yourusername/internship-report-generator",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: Education",
        "Intended Audience :: Students",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Education",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Text Processing :: Linguistic",
    ],
    python_requires=">=3.9",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=7.4.0",
            "pytest-cov>=4.1.0",
            "pytest-asyncio>=0.21.0",
            "black>=23.12.0",
            "flake8>=6.1.0",
            "mypy>=1.8.0",
            "bandit>=1.7.5",
            "safety>=2.3.0",
        ],
        "monitoring": [
            "prometheus-client>=0.19.0",
            "sentry-sdk>=1.40.0",
        ],
        "cloud": [
            "boto3>=1.34.0",
            "google-cloud-storage>=2.10.0",
        ]
    },
    entry_points={
        "console_scripts": [
            "internship-report-generator=streamlit_app.main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.yaml", "*.yml", "*.json", "*.css", "*.html", "*.md"],
    },
    project_urls={
        "Bug Reports": "https://github.com/yourusername/internship-report-generator/issues",
        "Source": "https://github.com/yourusername/internship-report-generator",
        "Documentation": "https://github.com/yourusername/internship-report-generator/docs",
    },
)
