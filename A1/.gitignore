# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
#   For a library or package, you might want to ignore these files since the code is
#   intended to run in multiple environments; otherwise, check them in:
# .python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# poetry
#   Similar to Pipfile.lock, it is generally recommended to include poetry.lock in version control.
#   This is especially recommended for binary packages to ensure reproducibility, and is more
#   commonly ignored for libraries.
#   https://python-poetry.org/docs/basic-usage/#commit-your-poetrylock-file-to-version-control
#poetry.lock

# pdm
#   Similar to Pipfile.lock, it is generally recommended to include pdm.lock in version control.
#pdm.lock
#   pdm stores project-wide configurations in .pdm.toml, but it is recommended to not include it
#   in version control.
#   https://pdm.fming.dev/#use-with-ide
.pdm.toml

# PEP 582; used by e.g. github.com/David-OConnor/pyflow and github.com/pdm-project/pdm
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# PyCharm
#  JetBrains specific template is maintained in a separate JetBrains.gitignore that can
#  be added to the global gitignore or merged into this project gitignore.  For a PyCharm
#  project, it is recommended to include the following files in version control:
#  - .idea/modules.xml
#  - .idea/*.iml
#  - .idea/misc.xml
#  - .idea/vcs.xml
.idea/

# VS Code
.vscode/
*.code-workspace

# Streamlit
.streamlit/secrets.toml

# Application specific
uploads/
temp/
logs/
data/
*.db
*.sqlite
*.sqlite3

# Google OAuth credentials
google_credentials.json
client_secret.json
credentials.json

# API keys and secrets
.env.local
.env.production
.env.staging
secrets/

# Processing outputs
processing_outputs/
enhanced_reports/
generated_reports/

# Temporary files
*.tmp
*.temp
.DS_Store
Thumbs.db

# Docker
.dockerignore
docker-compose.override.yml

# Monitoring and logs
prometheus_data/
grafana_data/
elasticsearch_data/
nginx_logs/

# Security scan results
bandit-report.json
safety-report.json
security-reports/

# Performance test results
lighthouse-results/
.lighthouseci/

# Backup files
*.bak
*.backup
*.old

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
*.swp
*.swo
*~

# Local configuration
config/local.yaml
config/development.yaml

# Test artifacts
test-results/
test-reports/

# Coverage reports
htmlcov/
.coverage
coverage.xml

# Profiling
*.prof

# Memory dumps
*.hprof

# Application logs
app.log
error.log
access.log

# Email templates (if containing sensitive info)
# templates/email/*.html

# User uploaded files (in development)
user_uploads/
user_data/

# Cache directories
.cache/
__pycache__/
.pytest_cache/

# Build artifacts
build/
dist/
*.egg-info/

# Documentation build
docs/_build/
docs/build/

# Jupyter notebooks (if any)
*.ipynb

# Virtual environments
venv*/
env*/
.venv*/

# Package files
*.tar.gz
*.zip
*.rar

# Local development database
local.db
dev.db
test.db

# Redis dump
dump.rdb

# Node modules (if any frontend components)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Terraform (if used for infrastructure)
*.tfstate
*.tfstate.*
.terraform/

# Kubernetes (if used for deployment)
*.kubeconfig

# Helm charts (if used)
charts/*/charts/
charts/*/requirements.lock

# Local scripts
scripts/local/
scripts/dev/

# Temporary processing files
*.processing
*.tmp_*

# AI model cache
.cache/huggingface/
.cache/transformers/

# Large files that shouldn't be in git
*.pdf
*.docx
*.png
*.jpg
*.jpeg
*.gif
*.mp4
*.avi
*.mov

# Exception: Allow sample files in specific directories
!samples/*.pdf
!samples/*.docx
!templates/*.pdf
!templates/*.docx
!docs/images/*.png
!docs/images/*.jpg

# Local testing files
test_*.py.local
*_test_local.py
