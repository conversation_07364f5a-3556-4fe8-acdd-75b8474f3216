"""
File Handler Utility
Handles file upload, validation, and processing for the internship report generator
"""

import os
import shutil
import hashlib
import mimetypes
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
import streamlit as st
from datetime import datetime
import tempfile
import magic

class FileHandler:
    """File handling utility class"""
    
    def __init__(self):
        """Initialize file handler with configuration"""
        self.upload_folder = Path(self._get_config("UPLOAD_FOLDER", "./uploads"))
        self.temp_folder = Path(self._get_config("TEMP_FOLDER", "./temp"))
        self.max_file_size = int(self._get_config("MAX_FILE_SIZE_MB", "50")) * 1024 * 1024  # Convert to bytes
        self.allowed_types = self._get_config("ALLOWED_FILE_TYPES", "pdf,docx,txt,png,jpg,jpeg").split(",")
        
        # Create directories if they don't exist
        self.upload_folder.mkdir(parents=True, exist_ok=True)
        self.temp_folder.mkdir(parents=True, exist_ok=True)
    
    def _get_config(self, key: str, default: str = None) -> str:
        """Get configuration value from environment or Streamlit secrets"""
        try:
            return st.secrets["general"][key]
        except (KeyError, FileNotFoundError):
            return os.getenv(key, default)
    
    def validate_file(self, uploaded_file) -> Tuple[bool, str]:
        """Validate uploaded file"""
        try:
            # Check file size
            if uploaded_file.size > self.max_file_size:
                return False, f"File size ({uploaded_file.size / 1024 / 1024:.1f} MB) exceeds maximum allowed size ({self.max_file_size / 1024 / 1024} MB)"
            
            # Check file extension
            file_extension = Path(uploaded_file.name).suffix.lower().lstrip('.')
            if file_extension not in self.allowed_types:
                return False, f"File type '{file_extension}' not allowed. Allowed types: {', '.join(self.allowed_types)}"
            
            # Check MIME type for additional security
            mime_type = self._get_mime_type(uploaded_file)
            if not self._is_mime_type_allowed(mime_type, file_extension):
                return False, f"File content does not match extension. Detected MIME type: {mime_type}"
            
            return True, "File validation successful"
            
        except Exception as e:
            return False, f"File validation error: {str(e)}"
    
    def _get_mime_type(self, uploaded_file) -> str:
        """Get MIME type of uploaded file"""
        try:
            # Read first few bytes to determine MIME type
            file_bytes = uploaded_file.read(1024)
            uploaded_file.seek(0)  # Reset file pointer
            
            # Use python-magic for MIME type detection
            mime_type = magic.from_buffer(file_bytes, mime=True)
            return mime_type
        except Exception:
            # Fallback to mimetypes module
            mime_type, _ = mimetypes.guess_type(uploaded_file.name)
            return mime_type or "application/octet-stream"
    
    def _is_mime_type_allowed(self, mime_type: str, file_extension: str) -> bool:
        """Check if MIME type matches expected type for file extension"""
        allowed_mime_types = {
            'pdf': ['application/pdf'],
            'docx': ['application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
            'txt': ['text/plain', 'text/x-python', 'application/x-empty'],
            'png': ['image/png'],
            'jpg': ['image/jpeg'],
            'jpeg': ['image/jpeg'],
            'tiff': ['image/tiff'],
            'bmp': ['image/bmp']
        }
        
        expected_types = allowed_mime_types.get(file_extension, [])
        return mime_type in expected_types or mime_type.startswith('text/') if file_extension == 'txt' else mime_type in expected_types
    
    def save_uploaded_file(self, uploaded_file, user_id: int) -> Dict[str, Any]:
        """Save uploaded file to storage"""
        try:
            # Validate file first
            is_valid, message = self.validate_file(uploaded_file)
            if not is_valid:
                return {"success": False, "error": message}
            
            # Generate unique filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_hash = self._generate_file_hash(uploaded_file)
            file_extension = Path(uploaded_file.name).suffix
            
            unique_filename = f"{user_id}_{timestamp}_{file_hash[:8]}{file_extension}"
            file_path = self.upload_folder / unique_filename
            
            # Save file
            with open(file_path, "wb") as f:
                f.write(uploaded_file.getbuffer())
            
            # Get file info
            file_info = {
                "success": True,
                "original_filename": uploaded_file.name,
                "saved_filename": unique_filename,
                "file_path": str(file_path),
                "file_size": uploaded_file.size,
                "file_type": Path(uploaded_file.name).suffix.lower().lstrip('.'),
                "mime_type": self._get_mime_type(uploaded_file),
                "upload_timestamp": datetime.now().isoformat(),
                "file_hash": file_hash
            }
            
            return file_info
            
        except Exception as e:
            return {"success": False, "error": f"Error saving file: {str(e)}"}
    
    def _generate_file_hash(self, uploaded_file) -> str:
        """Generate SHA-256 hash of file content"""
        try:
            file_content = uploaded_file.getbuffer()
            return hashlib.sha256(file_content).hexdigest()
        except Exception:
            return hashlib.sha256(str(datetime.now()).encode()).hexdigest()
    
    def create_temp_file(self, content: str, extension: str = ".txt") -> str:
        """Create temporary file with content"""
        try:
            temp_file = tempfile.NamedTemporaryFile(
                mode='w',
                suffix=extension,
                dir=self.temp_folder,
                delete=False,
                encoding='utf-8'
            )
            
            temp_file.write(content)
            temp_file.close()
            
            return temp_file.name
            
        except Exception as e:
            raise Exception(f"Error creating temporary file: {str(e)}")
    
    def read_file_content(self, file_path: str) -> Dict[str, Any]:
        """Read content from file"""
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                return {"success": False, "error": "File not found"}
            
            file_extension = file_path.suffix.lower()
            
            if file_extension == '.txt':
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
            elif file_extension in ['.pdf', '.docx', '.png', '.jpg', '.jpeg']:
                # These require special parsing - return file path for processing
                return {
                    "success": True,
                    "file_path": str(file_path),
                    "requires_parsing": True,
                    "file_type": file_extension.lstrip('.')
                }
            else:
                return {"success": False, "error": f"Unsupported file type: {file_extension}"}
            
            return {
                "success": True,
                "content": content,
                "file_path": str(file_path),
                "file_type": file_extension.lstrip('.'),
                "file_size": file_path.stat().st_size
            }
            
        except Exception as e:
            return {"success": False, "error": f"Error reading file: {str(e)}"}
    
    def delete_file(self, file_path: str) -> bool:
        """Delete file from storage"""
        try:
            file_path = Path(file_path)
            if file_path.exists():
                file_path.unlink()
                return True
            return False
        except Exception:
            return False
    
    def cleanup_temp_files(self, max_age_hours: int = 24):
        """Clean up temporary files older than specified hours"""
        try:
            current_time = datetime.now()
            
            for file_path in self.temp_folder.glob("*"):
                if file_path.is_file():
                    file_age = current_time - datetime.fromtimestamp(file_path.stat().st_mtime)
                    if file_age.total_seconds() > max_age_hours * 3600:
                        file_path.unlink()
                        
        except Exception as e:
            st.warning(f"Error during cleanup: {str(e)}")
    
    def get_file_info(self, file_path: str) -> Dict[str, Any]:
        """Get detailed file information"""
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                return {"error": "File not found"}
            
            stat = file_path.stat()
            
            return {
                "filename": file_path.name,
                "file_size": stat.st_size,
                "file_size_mb": round(stat.st_size / 1024 / 1024, 2),
                "created_time": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                "modified_time": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                "file_extension": file_path.suffix.lower(),
                "absolute_path": str(file_path.absolute())
            }
            
        except Exception as e:
            return {"error": f"Error getting file info: {str(e)}"}
    
    def create_download_link(self, file_path: str, download_name: str = None) -> Optional[str]:
        """Create download link for processed file"""
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                return None
            
            # Read file content
            with open(file_path, 'rb') as f:
                file_content = f.read()
            
            # Create download button in Streamlit
            download_name = download_name or file_path.name
            
            st.download_button(
                label=f"📥 Download {download_name}",
                data=file_content,
                file_name=download_name,
                mime=self._get_download_mime_type(file_path.suffix)
            )
            
            return str(file_path)
            
        except Exception as e:
            st.error(f"Error creating download link: {str(e)}")
            return None
    
    def _get_download_mime_type(self, file_extension: str) -> str:
        """Get appropriate MIME type for download"""
        mime_types = {
            '.pdf': 'application/pdf',
            '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            '.txt': 'text/plain',
            '.md': 'text/markdown',
            '.json': 'application/json'
        }
        
        return mime_types.get(file_extension.lower(), 'application/octet-stream')
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """Get storage usage statistics"""
        try:
            upload_size = sum(f.stat().st_size for f in self.upload_folder.rglob('*') if f.is_file())
            temp_size = sum(f.stat().st_size for f in self.temp_folder.rglob('*') if f.is_file())
            
            upload_count = len([f for f in self.upload_folder.rglob('*') if f.is_file()])
            temp_count = len([f for f in self.temp_folder.rglob('*') if f.is_file()])
            
            return {
                "upload_folder_size_mb": round(upload_size / 1024 / 1024, 2),
                "temp_folder_size_mb": round(temp_size / 1024 / 1024, 2),
                "total_size_mb": round((upload_size + temp_size) / 1024 / 1024, 2),
                "upload_file_count": upload_count,
                "temp_file_count": temp_count,
                "total_file_count": upload_count + temp_count
            }
            
        except Exception as e:
            return {"error": f"Error getting storage stats: {str(e)}"}

class FileValidator:
    """Additional file validation utilities"""
    
    @staticmethod
    def is_text_file_readable(file_path: str) -> bool:
        """Check if text file is readable and contains valid text"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read(1000)  # Read first 1000 characters
                return len(content.strip()) > 0
        except Exception:
            return False
    
    @staticmethod
    def estimate_processing_time(file_size_mb: float, file_type: str) -> int:
        """Estimate processing time in seconds based on file size and type"""
        base_times = {
            'txt': 5,    # 5 seconds base
            'pdf': 15,   # 15 seconds base
            'docx': 10,  # 10 seconds base
            'png': 20,   # 20 seconds base (OCR)
            'jpg': 20,   # 20 seconds base (OCR)
            'jpeg': 20   # 20 seconds base (OCR)
        }
        
        base_time = base_times.get(file_type, 10)
        size_factor = max(1, file_size_mb / 5)  # Add time for every 5MB
        
        return int(base_time * size_factor)
    
    @staticmethod
    def check_file_corruption(file_path: str) -> bool:
        """Basic check for file corruption"""
        try:
            file_path = Path(file_path)
            
            # Check if file exists and has content
            if not file_path.exists() or file_path.stat().st_size == 0:
                return True  # Corrupted
            
            # Try to read file
            with open(file_path, 'rb') as f:
                f.read(1024)  # Try to read first 1KB
            
            return False  # Not corrupted
            
        except Exception:
            return True  # Assume corrupted if any error occurs
