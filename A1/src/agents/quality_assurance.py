"""
Quality Assurance Agent
Specialized agent for final validation and compliance checking
"""

import re
import json
from typing import Dict, Any, List, Optional
from pathlib import Path
import statistics

from crewai.tools import BaseTool
from pydantic import BaseModel, Field

class ComplianceCheckerTool(BaseTool):
    """Tool for checking academic and professional compliance standards"""
    name: str = "compliance_checker"
    description: str = "Check compliance with academic and professional standards"
    
    def _run(self, content: str, standards: List[str] = None) -> Dict[str, Any]:
        """Check compliance with specified standards"""
        try:
            if not standards:
                standards = ['academic', 'professional', 'technical']
            
            compliance_results = {}
            overall_score = 0
            
            for standard in standards:
                result = self._check_standard(content, standard)
                compliance_results[standard] = result
                overall_score += result['score']
            
            overall_score = overall_score / len(standards) if standards else 0
            
            return {
                "compliance_results": compliance_results,
                "overall_score": overall_score,
                "recommendations": self._generate_compliance_recommendations(compliance_results),
                "standards_checked": standards
            }
        except Exception as e:
            return {"error": f"Compliance checking failed: {str(e)}"}
    
    def _check_standard(self, content: str, standard: str) -> Dict[str, Any]:
        """Check specific compliance standard"""
        if standard == 'academic':
            return self._check_academic_standard(content)
        elif standard == 'professional':
            return self._check_professional_standard(content)
        elif standard == 'technical':
            return self._check_technical_standard(content)
        else:
            return {"score": 0, "issues": [f"Unknown standard: {standard}"]}
    
    def _check_academic_standard(self, content: str) -> Dict[str, Any]:
        """Check academic writing standards"""
        score = 100.0
        issues = []
        
        # Check for proper structure
        headings = re.findall(r'^#{1,6}\s+(.+)', content, re.MULTILINE)
        if len(headings) < 3:
            score -= 20
            issues.append("Insufficient section headings (minimum 3 required)")
        
        # Check for introduction and conclusion
        content_lower = content.lower()
        if not any(word in content_lower for word in ['introduction', 'overview', 'background']):
            score -= 15
            issues.append("Missing introduction section")
        
        if not any(word in content_lower for word in ['conclusion', 'summary', 'final']):
            score -= 15
            issues.append("Missing conclusion section")
        
        # Check word count
        word_count = len(content.split())
        if word_count < 1000:
            score -= 20
            issues.append(f"Document too short ({word_count} words, minimum 1000 recommended)")
        
        # Check for proper paragraphs
        paragraphs = [p.strip() for p in content.split('\n\n') if p.strip()]
        short_paragraphs = [p for p in paragraphs if len(p.split()) < 30]
        if len(short_paragraphs) > len(paragraphs) * 0.3:
            score -= 10
            issues.append("Too many short paragraphs (less than 30 words)")
        
        # Check for citations (basic)
        citations = re.findall(r'\([^)]*\d{4}[^)]*\)', content)
        if len(citations) == 0:
            score -= 10
            issues.append("No citations found")
        
        return {
            "score": max(0, score),
            "issues": issues,
            "word_count": word_count,
            "section_count": len(headings),
            "citation_count": len(citations)
        }
    
    def _check_professional_standard(self, content: str) -> Dict[str, Any]:
        """Check professional writing standards"""
        score = 100.0
        issues = []
        
        # Check for professional language
        informal_words = [
            'gonna', 'wanna', 'kinda', 'sorta', 'yeah', 'ok', 'okay',
            'stuff', 'things', 'lots of', 'a lot of', 'pretty good', 'really'
        ]
        
        informal_count = 0
        for word in informal_words:
            informal_count += len(re.findall(r'\b' + word + r'\b', content, re.IGNORECASE))
        
        if informal_count > 0:
            score -= min(30, informal_count * 5)
            issues.append(f"Found {informal_count} instances of informal language")
        
        # Check for first person overuse
        first_person = len(re.findall(r'\bI\b', content))
        word_count = len(content.split())
        first_person_ratio = (first_person / word_count * 100) if word_count > 0 else 0
        
        if first_person_ratio > 5:
            score -= 15
            issues.append(f"Excessive use of first person ({first_person_ratio:.1f}%)")
        
        # Check for proper tone
        emotional_words = [
            'amazing', 'awesome', 'terrible', 'horrible', 'love', 'hate',
            'super', 'extremely', 'totally', 'absolutely'
        ]
        
        emotional_count = 0
        for word in emotional_words:
            emotional_count += len(re.findall(r'\b' + word + r'\b', content, re.IGNORECASE))
        
        if emotional_count > 3:
            score -= 10
            issues.append("Overly emotional language detected")
        
        # Check for passive voice (simplified)
        passive_indicators = re.findall(r'\b(was|were|been|being)\s+\w+ed\b', content, re.IGNORECASE)
        passive_ratio = (len(passive_indicators) / word_count * 100) if word_count > 0 else 0
        
        if passive_ratio > 10:
            score -= 10
            issues.append(f"High passive voice usage ({passive_ratio:.1f}%)")
        
        return {
            "score": max(0, score),
            "issues": issues,
            "informal_count": informal_count,
            "first_person_ratio": first_person_ratio,
            "passive_ratio": passive_ratio
        }
    
    def _check_technical_standard(self, content: str) -> Dict[str, Any]:
        """Check technical writing standards"""
        score = 100.0
        issues = []
        
        # Check for technical terminology
        technical_terms = [
            'implementation', 'architecture', 'framework', 'methodology',
            'optimization', 'integration', 'scalability', 'performance',
            'security', 'authentication', 'authorization', 'encryption',
            'algorithm', 'data structure', 'design pattern', 'best practices'
        ]
        
        technical_count = 0
        for term in technical_terms:
            technical_count += len(re.findall(r'\b' + term + r'\b', content, re.IGNORECASE))
        
        word_count = len(content.split())
        technical_density = (technical_count / word_count * 100) if word_count > 0 else 0
        
        if technical_density < 2:
            score -= 20
            issues.append(f"Low technical terminology density ({technical_density:.1f}%)")
        
        # Check for specific technical details
        code_mentions = len(re.findall(r'\b(code|programming|development|software)\b', content, re.IGNORECASE))
        if code_mentions < 5:
            score -= 15
            issues.append("Insufficient technical detail")
        
        # Check for technology mentions
        technologies = [
            'python', 'javascript', 'java', 'html', 'css', 'sql', 'react',
            'angular', 'vue', 'node', 'express', 'django', 'flask', 'git'
        ]
        
        tech_mentions = 0
        for tech in technologies:
            tech_mentions += len(re.findall(r'\b' + tech + r'\b', content, re.IGNORECASE))
        
        if tech_mentions < 3:
            score -= 10
            issues.append("Few specific technologies mentioned")
        
        return {
            "score": max(0, score),
            "issues": issues,
            "technical_density": technical_density,
            "code_mentions": code_mentions,
            "tech_mentions": tech_mentions
        }
    
    def _generate_compliance_recommendations(self, compliance_results: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on compliance results"""
        recommendations = []
        
        for standard, result in compliance_results.items():
            if result['score'] < 70:
                recommendations.append(f"Improve {standard} compliance (score: {result['score']:.1f})")
            
            for issue in result.get('issues', []):
                recommendations.append(f"{standard.title()}: {issue}")
        
        return recommendations

class QualityMetricsTool(BaseTool):
    """Tool for calculating comprehensive quality metrics"""
    name: str = "quality_metrics"
    description: str = "Calculate comprehensive quality metrics for the document"
    
    def _run(self, content: str, enhancement_results: Dict[str, Any] = None) -> Dict[str, Any]:
        """Calculate quality metrics"""
        try:
            metrics = {
                "readability": self._calculate_readability(content),
                "coherence": self._calculate_coherence(content),
                "completeness": self._calculate_completeness(content),
                "technical_depth": self._calculate_technical_depth(content),
                "professional_quality": self._calculate_professional_quality(content)
            }
            
            # Include enhancement scores if available
            if enhancement_results:
                metrics.update(self._extract_enhancement_scores(enhancement_results))
            
            # Calculate overall quality score
            overall_score = statistics.mean(metrics.values())
            
            return {
                "metrics": metrics,
                "overall_score": overall_score,
                "grade": self._assign_grade(overall_score),
                "improvement_areas": self._identify_improvement_areas(metrics)
            }
        except Exception as e:
            return {"error": f"Quality metrics calculation failed: {str(e)}"}
    
    def _calculate_readability(self, content: str) -> float:
        """Calculate readability score"""
        sentences = re.split(r'[.!?]+', content)
        sentences = [s.strip() for s in sentences if s.strip()]
        
        if not sentences:
            return 0.0
        
        words = content.split()
        avg_sentence_length = len(words) / len(sentences)
        
        # Simple readability metric (lower sentence length = higher readability)
        readability_score = max(0, 100 - (avg_sentence_length - 15) * 2)
        return min(100.0, readability_score)
    
    def _calculate_coherence(self, content: str) -> float:
        """Calculate content coherence score"""
        paragraphs = [p.strip() for p in content.split('\n\n') if p.strip()]
        
        if len(paragraphs) < 2:
            return 50.0
        
        # Check for transition words
        transition_words = [
            'furthermore', 'additionally', 'moreover', 'however', 'therefore',
            'consequently', 'meanwhile', 'in addition', 'as a result',
            'for example', 'in conclusion', 'first', 'second', 'finally'
        ]
        
        transition_count = 0
        for word in transition_words:
            transition_count += len(re.findall(r'\b' + word + r'\b', content, re.IGNORECASE))
        
        # Score based on transition density
        word_count = len(content.split())
        transition_density = (transition_count / word_count * 100) if word_count > 0 else 0
        
        coherence_score = min(100.0, transition_density * 20 + 40)
        return coherence_score
    
    def _calculate_completeness(self, content: str) -> float:
        """Calculate content completeness score"""
        required_sections = [
            'introduction', 'background', 'methodology', 'implementation',
            'results', 'conclusion'
        ]
        
        content_lower = content.lower()
        found_sections = 0
        
        for section in required_sections:
            if section in content_lower:
                found_sections += 1
        
        completeness_score = (found_sections / len(required_sections)) * 100
        return completeness_score
    
    def _calculate_technical_depth(self, content: str) -> float:
        """Calculate technical depth score"""
        technical_indicators = [
            'implementation', 'architecture', 'algorithm', 'optimization',
            'performance', 'scalability', 'security', 'testing', 'debugging',
            'deployment', 'integration', 'framework', 'library', 'api'
        ]
        
        technical_count = 0
        for indicator in technical_indicators:
            technical_count += len(re.findall(r'\b' + indicator + r'\b', content, re.IGNORECASE))
        
        word_count = len(content.split())
        technical_density = (technical_count / word_count * 100) if word_count > 0 else 0
        
        depth_score = min(100.0, technical_density * 15)
        return depth_score
    
    def _calculate_professional_quality(self, content: str) -> float:
        """Calculate professional quality score"""
        # Check for professional language patterns
        professional_terms = [
            'developed', 'implemented', 'designed', 'analyzed', 'evaluated',
            'collaborated', 'contributed', 'achieved', 'accomplished', 'delivered'
        ]
        
        professional_count = 0
        for term in professional_terms:
            professional_count += len(re.findall(r'\b' + term + r'\b', content, re.IGNORECASE))
        
        # Check for proper formatting
        headings = re.findall(r'^#{1,6}\s+', content, re.MULTILINE)
        proper_formatting = len(headings) >= 3
        
        # Check for appropriate length
        word_count = len(content.split())
        appropriate_length = 1000 <= word_count <= 5000
        
        # Calculate score
        score = 0
        score += min(40, professional_count * 3)  # Professional language
        score += 30 if proper_formatting else 0   # Proper formatting
        score += 30 if appropriate_length else 15 # Appropriate length
        
        return min(100.0, score)
    
    def _extract_enhancement_scores(self, enhancement_results: Dict[str, Any]) -> Dict[str, float]:
        """Extract scores from enhancement results"""
        scores = {}
        
        # Extract scores from different enhancement stages
        if 'improvement_score' in enhancement_results:
            scores['content_improvement'] = enhancement_results['improvement_score']
        
        if 'technical_score' in enhancement_results:
            scores['technical_enhancement'] = enhancement_results['technical_score']
        
        if 'structure_score' in enhancement_results:
            scores['structure_optimization'] = enhancement_results['structure_score']
        
        if 'visual_score' in enhancement_results:
            scores['visual_integration'] = enhancement_results['visual_score']
        
        return scores
    
    def _assign_grade(self, score: float) -> str:
        """Assign letter grade based on score"""
        if score >= 90:
            return 'A'
        elif score >= 80:
            return 'B'
        elif score >= 70:
            return 'C'
        elif score >= 60:
            return 'D'
        else:
            return 'F'
    
    def _identify_improvement_areas(self, metrics: Dict[str, float]) -> List[str]:
        """Identify areas needing improvement"""
        improvement_areas = []
        
        for metric, score in metrics.items():
            if score < 70:
                improvement_areas.append(f"Improve {metric.replace('_', ' ')} (score: {score:.1f})")
        
        return improvement_areas

class FinalValidatorTool(BaseTool):
    """Tool for final validation and sign-off"""
    name: str = "final_validator"
    description: str = "Perform final validation and generate quality report"
    
    def _run(self, content: str, all_results: Dict[str, Any]) -> Dict[str, Any]:
        """Perform final validation"""
        try:
            validation_report = {
                "content_length": len(content.split()),
                "character_count": len(content),
                "section_count": len(re.findall(r'^#{1,6}\s+', content, re.MULTILINE)),
                "paragraph_count": len([p for p in content.split('\n\n') if p.strip()]),
                "validation_timestamp": self._get_timestamp(),
                "processing_summary": self._generate_processing_summary(all_results),
                "quality_assessment": self._generate_quality_assessment(all_results),
                "final_recommendations": self._generate_final_recommendations(all_results),
                "approval_status": self._determine_approval_status(all_results)
            }
            
            return validation_report
        except Exception as e:
            return {"error": f"Final validation failed: {str(e)}"}
    
    def _get_timestamp(self) -> str:
        """Get current timestamp"""
        from datetime import datetime
        return datetime.now().isoformat()
    
    def _generate_processing_summary(self, all_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate summary of all processing steps"""
        summary = {
            "stages_completed": [],
            "total_changes": 0,
            "processing_time": 0
        }
        
        # Extract information from results
        for stage, results in all_results.items():
            if isinstance(results, dict) and "changes_made" in results:
                summary["stages_completed"].append(stage)
                summary["total_changes"] += len(results["changes_made"])
            
            if isinstance(results, dict) and "processing_time" in results:
                summary["processing_time"] += results["processing_time"]
        
        return summary
    
    def _generate_quality_assessment(self, all_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate overall quality assessment"""
        scores = []
        
        # Collect all scores
        for results in all_results.values():
            if isinstance(results, dict):
                for key, value in results.items():
                    if 'score' in key and isinstance(value, (int, float)):
                        scores.append(value)
        
        if scores:
            avg_score = statistics.mean(scores)
            min_score = min(scores)
            max_score = max(scores)
        else:
            avg_score = min_score = max_score = 0
        
        return {
            "average_score": avg_score,
            "minimum_score": min_score,
            "maximum_score": max_score,
            "score_count": len(scores),
            "overall_grade": self._assign_grade(avg_score)
        }
    
    def _generate_final_recommendations(self, all_results: Dict[str, Any]) -> List[str]:
        """Generate final recommendations"""
        recommendations = []
        
        # Collect recommendations from all stages
        for results in all_results.values():
            if isinstance(results, dict) and "recommendations" in results:
                if isinstance(results["recommendations"], list):
                    recommendations.extend(results["recommendations"])
        
        # Remove duplicates and return top recommendations
        unique_recommendations = list(set(recommendations))
        return unique_recommendations[:10]  # Top 10 recommendations
    
    def _determine_approval_status(self, all_results: Dict[str, Any]) -> str:
        """Determine if the document meets approval criteria"""
        quality_assessment = self._generate_quality_assessment(all_results)
        avg_score = quality_assessment["average_score"]
        
        if avg_score >= 85:
            return "APPROVED - Excellent Quality"
        elif avg_score >= 75:
            return "APPROVED - Good Quality"
        elif avg_score >= 65:
            return "CONDITIONAL - Minor Improvements Needed"
        else:
            return "REQUIRES REVISION - Significant Improvements Needed"
    
    def _assign_grade(self, score: float) -> str:
        """Assign letter grade based on score"""
        if score >= 90:
            return 'A'
        elif score >= 80:
            return 'B'
        elif score >= 70:
            return 'C'
        elif score >= 60:
            return 'D'
        else:
            return 'F'

class QualityAssurance:
    """Quality Assurance Agent - main class"""
    
    def __init__(self):
        """Initialize quality assurance with tools"""
        self.tools = [
            ComplianceCheckerTool(),
            QualityMetricsTool(),
            FinalValidatorTool()
        ]
    
    def get_tools(self) -> List[BaseTool]:
        """Get list of available tools"""
        return self.tools
    
    def perform_quality_assurance(self, content: str, all_results: Dict[str, Any] = None) -> Dict[str, Any]:
        """Main method to perform quality assurance"""
        try:
            if not all_results:
                all_results = {}
            
            qa_results = {}
            
            # Step 1: Compliance checking
            compliance_tool = self.tools[0]
            compliance_result = compliance_tool._run(content)
            qa_results["compliance"] = compliance_result
            
            # Step 2: Quality metrics calculation
            metrics_tool = self.tools[1]
            metrics_result = metrics_tool._run(content, all_results)
            qa_results["metrics"] = metrics_result
            
            # Step 3: Final validation
            validator_tool = self.tools[2]
            validation_result = validator_tool._run(content, all_results)
            qa_results["validation"] = validation_result
            
            # Compile final QA report
            final_qa_result = {
                "qa_results": qa_results,
                "overall_assessment": self._generate_overall_assessment(qa_results),
                "final_score": self._calculate_final_score(qa_results),
                "certification": self._generate_certification(qa_results),
                "next_steps": self._recommend_next_steps(qa_results)
            }
            
            return final_qa_result
            
        except Exception as e:
            return {"error": f"Quality assurance failed: {str(e)}"}
    
    def _generate_overall_assessment(self, qa_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate overall assessment summary"""
        assessment = {
            "strengths": [],
            "weaknesses": [],
            "critical_issues": []
        }
        
        # Analyze compliance results
        compliance = qa_results.get("compliance", {})
        if compliance.get("overall_score", 0) >= 80:
            assessment["strengths"].append("Strong compliance with standards")
        elif compliance.get("overall_score", 0) < 60:
            assessment["critical_issues"].append("Poor compliance with standards")
        
        # Analyze quality metrics
        metrics = qa_results.get("metrics", {})
        if metrics.get("overall_score", 0) >= 80:
            assessment["strengths"].append("High overall quality metrics")
        elif metrics.get("overall_score", 0) < 60:
            assessment["critical_issues"].append("Low quality metrics")
        
        return assessment
    
    def _calculate_final_score(self, qa_results: Dict[str, Any]) -> float:
        """Calculate final quality score"""
        scores = []
        
        # Collect scores from all QA components
        compliance = qa_results.get("compliance", {})
        if "overall_score" in compliance:
            scores.append(compliance["overall_score"])
        
        metrics = qa_results.get("metrics", {})
        if "overall_score" in metrics:
            scores.append(metrics["overall_score"])
        
        return statistics.mean(scores) if scores else 0.0
    
    def _generate_certification(self, qa_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate quality certification"""
        final_score = self._calculate_final_score(qa_results)
        
        certification = {
            "certified": final_score >= 70,
            "certification_level": self._get_certification_level(final_score),
            "valid_until": "Document revision",
            "certifying_agent": "Multi-Agent Quality Assurance System"
        }
        
        return certification
    
    def _get_certification_level(self, score: float) -> str:
        """Get certification level based on score"""
        if score >= 90:
            return "GOLD - Exceptional Quality"
        elif score >= 80:
            return "SILVER - High Quality"
        elif score >= 70:
            return "BRONZE - Acceptable Quality"
        else:
            return "NOT CERTIFIED - Below Standards"
    
    def _recommend_next_steps(self, qa_results: Dict[str, Any]) -> List[str]:
        """Recommend next steps based on QA results"""
        final_score = self._calculate_final_score(qa_results)
        next_steps = []
        
        if final_score >= 85:
            next_steps.append("Document ready for submission")
            next_steps.append("Consider peer review for additional feedback")
        elif final_score >= 70:
            next_steps.append("Address minor recommendations")
            next_steps.append("Final proofreading recommended")
        else:
            next_steps.append("Significant revision required")
            next_steps.append("Focus on critical issues identified")
            next_steps.append("Re-run quality assurance after revisions")
        
        return next_steps
