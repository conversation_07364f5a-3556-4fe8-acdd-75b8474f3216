"""
Test suite for authentication module
Tests Google OAuth integration and session management
"""

import pytest
import os
import tempfile
from unittest.mock import Mock, patch, MagicMock
import streamlit as st

# Import modules to test
import sys
from pathlib import Path
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

from auth.google_auth import GoogleAuthenticator

class TestGoogleAuthenticator:
    """Test cases for Google OAuth authenticator"""
    
    @pytest.fixture
    def authenticator(self):
        """Create authenticator instance for testing"""
        with patch.dict(os.environ, {
            'GOOGLE_CLIENT_ID': 'test_client_id',
            'GOOGLE_CLIENT_SECRET': 'test_client_secret',
            'GOOGLE_REDIRECT_URI': 'http://localhost:8501'
        }):
            return GoogleAuthenticator()
    
    def test_authenticator_initialization(self, authenticator):
        """Test authenticator initialization with environment variables"""
        assert authenticator.client_id == 'test_client_id'
        assert authenticator.client_secret == 'test_client_secret'
        assert authenticator.redirect_uri == 'http://localhost:8501'
        assert authenticator.auth_url == "https://accounts.google.com/o/oauth2/auth"
        assert authenticator.token_url == "https://oauth2.googleapis.com/token"
        assert authenticator.userinfo_url == "https://www.googleapis.com/oauth2/v2/userinfo"
    
    def test_missing_client_id_raises_error(self):
        """Test that missing client ID raises ValueError"""
        with patch.dict(os.environ, {}, clear=True):
            with pytest.raises(ValueError, match="Configuration key 'GOOGLE_CLIENT_ID' not found"):
                GoogleAuthenticator()
    
    def test_generate_state(self, authenticator):
        """Test state parameter generation"""
        state1 = authenticator._generate_state()
        state2 = authenticator._generate_state()
        
        assert len(state1) > 20  # Should be reasonably long
        assert state1 != state2  # Should be unique
        assert isinstance(state1, str)
    
    def test_generate_code_verifier(self, authenticator):
        """Test PKCE code verifier generation"""
        verifier1 = authenticator._generate_code_verifier()
        verifier2 = authenticator._generate_code_verifier()
        
        assert len(verifier1) > 20
        assert verifier1 != verifier2
        assert isinstance(verifier1, str)
    
    def test_generate_code_challenge(self, authenticator):
        """Test PKCE code challenge generation"""
        verifier = "test_verifier_123"
        challenge = authenticator._generate_code_challenge(verifier)
        
        assert isinstance(challenge, str)
        assert len(challenge) > 20
        # Same verifier should produce same challenge
        assert challenge == authenticator._generate_code_challenge(verifier)
    
    @patch('streamlit.session_state', {})
    def test_get_authorization_url(self, authenticator):
        """Test authorization URL generation"""
        auth_url, state, code_verifier = authenticator.get_authorization_url()
        
        assert auth_url.startswith("https://accounts.google.com/o/oauth2/auth")
        assert "client_id=test_client_id" in auth_url
        assert "redirect_uri=http://localhost:8501" in auth_url
        assert "scope=openid+email+profile" in auth_url
        assert "response_type=code" in auth_url
        assert f"state={state}" in auth_url
        assert "code_challenge=" in auth_url
        assert "code_challenge_method=S256" in auth_url
        
        # Check session state
        assert st.session_state['oauth_state'] == state
        assert st.session_state['code_verifier'] == code_verifier
    
    @patch('requests.post')
    @patch('streamlit.session_state', {'oauth_state': 'test_state', 'code_verifier': 'test_verifier'})
    def test_exchange_code_for_token_success(self, mock_post, authenticator):
        """Test successful token exchange"""
        # Mock successful response
        mock_response = Mock()
        mock_response.json.return_value = {
            'access_token': 'test_access_token',
            'refresh_token': 'test_refresh_token',
            'expires_in': 3600
        }
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response
        
        result = authenticator.exchange_code_for_token('test_code', 'test_state')
        
        assert result is not None
        assert result['access_token'] == 'test_access_token'
        assert result['refresh_token'] == 'test_refresh_token'
        
        # Verify request was made correctly
        mock_post.assert_called_once()
        call_args = mock_post.call_args
        assert call_args[0][0] == authenticator.token_url
        assert call_args[1]['data']['code'] == 'test_code'
        assert call_args[1]['data']['client_id'] == 'test_client_id'
    
    @patch('streamlit.session_state', {'oauth_state': 'different_state'})
    def test_exchange_code_invalid_state(self, authenticator):
        """Test token exchange with invalid state"""
        with pytest.raises(ValueError, match="Invalid state parameter"):
            authenticator.exchange_code_for_token('test_code', 'wrong_state')
    
    @patch('streamlit.session_state', {'oauth_state': 'test_state'})
    def test_exchange_code_missing_verifier(self, authenticator):
        """Test token exchange with missing code verifier"""
        with pytest.raises(ValueError, match="Code verifier not found"):
            authenticator.exchange_code_for_token('test_code', 'test_state')
    
    @patch('requests.post')
    @patch('streamlit.session_state', {'oauth_state': 'test_state', 'code_verifier': 'test_verifier'})
    def test_exchange_code_request_failure(self, mock_post, authenticator):
        """Test token exchange with request failure"""
        mock_post.side_effect = Exception("Network error")
        
        with patch('streamlit.error') as mock_error:
            result = authenticator.exchange_code_for_token('test_code', 'test_state')
            
            assert result is None
            mock_error.assert_called_once()
    
    @patch('requests.get')
    def test_get_user_info_success(self, mock_get, authenticator):
        """Test successful user info retrieval"""
        mock_response = Mock()
        mock_response.json.return_value = {
            'id': '123456789',
            'email': '<EMAIL>',
            'name': 'Test User',
            'picture': 'https://example.com/photo.jpg'
        }
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        result = authenticator.get_user_info('test_access_token')
        
        assert result is not None
        assert result['id'] == '123456789'
        assert result['email'] == '<EMAIL>'
        assert result['name'] == 'Test User'
        
        # Verify request headers
        mock_get.assert_called_once_with(
            authenticator.userinfo_url,
            headers={'Authorization': 'Bearer test_access_token'}
        )
    
    @patch('requests.get')
    def test_get_user_info_failure(self, mock_get, authenticator):
        """Test user info retrieval failure"""
        mock_get.side_effect = Exception("API error")
        
        with patch('streamlit.error') as mock_error:
            result = authenticator.get_user_info('test_access_token')
            
            assert result is None
            mock_error.assert_called_once()
    
    @patch('streamlit.session_state', {})
    def test_is_authenticated_false(self, authenticator):
        """Test authentication check when not authenticated"""
        assert not authenticator.is_authenticated()
    
    @patch('streamlit.session_state', {
        'authenticated': True,
        'access_token': 'test_token'
    })
    def test_is_authenticated_true(self, authenticator):
        """Test authentication check when authenticated"""
        assert authenticator.is_authenticated()
    
    @patch('streamlit.session_state', {
        'authenticated': True,
        'user_info': {'name': 'Test User', 'email': '<EMAIL>'}
    })
    def test_get_current_user(self, authenticator):
        """Test getting current user info"""
        user_info = authenticator.get_current_user()
        
        assert user_info is not None
        assert user_info['name'] == 'Test User'
        assert user_info['email'] == '<EMAIL>'
    
    @patch('streamlit.session_state', {
        'access_token': 'test_token',
        'refresh_token': 'test_refresh',
        'oauth_state': 'test_state',
        'code_verifier': 'test_verifier',
        'authenticated': True,
        'user_info': {'name': 'Test User'}
    })
    def test_logout(self, authenticator):
        """Test logout functionality"""
        with patch('streamlit.success') as mock_success:
            authenticator.logout()
            
            # Check that session state is cleared
            assert 'access_token' not in st.session_state
            assert 'refresh_token' not in st.session_state
            assert 'oauth_state' not in st.session_state
            assert 'code_verifier' not in st.session_state
            assert 'authenticated' not in st.session_state
            assert 'user_info' not in st.session_state
            
            mock_success.assert_called_once_with("Logged out successfully!")

class TestAuthenticationIntegration:
    """Integration tests for authentication flow"""
    
    @patch('streamlit.experimental_get_query_params')
    @patch('streamlit.experimental_set_query_params')
    @patch('streamlit.session_state', {})
    def test_full_authentication_flow(self, mock_set_params, mock_get_params):
        """Test complete authentication flow"""
        with patch.dict(os.environ, {
            'GOOGLE_CLIENT_ID': 'test_client_id',
            'GOOGLE_CLIENT_SECRET': 'test_client_secret'
        }):
            authenticator = GoogleAuthenticator()
            
            # Test initial state - no query params
            mock_get_params.return_value = {}
            
            with patch('streamlit.markdown') as mock_markdown:
                result = authenticator.authenticate()
                assert result is None
                mock_markdown.assert_called()  # Should display auth link
            
            # Test with authorization code in URL
            mock_get_params.return_value = {
                'code': ['test_auth_code'],
                'state': ['test_state']
            }
            
            st.session_state['oauth_state'] = 'test_state'
            st.session_state['code_verifier'] = 'test_verifier'
            
            with patch.object(authenticator, 'exchange_code_for_token') as mock_exchange, \
                 patch.object(authenticator, 'get_user_info') as mock_get_user:
                
                mock_exchange.return_value = {'access_token': 'test_token'}
                mock_get_user.return_value = {
                    'id': '123',
                    'email': '<EMAIL>',
                    'name': 'Test User'
                }
                
                result = authenticator.authenticate()
                
                assert result is not None
                assert result['email'] == '<EMAIL>'
                assert st.session_state['access_token'] == 'test_token'
                mock_set_params.assert_called_once_with()  # Should clear URL params

if __name__ == '__main__':
    pytest.main([__file__])
