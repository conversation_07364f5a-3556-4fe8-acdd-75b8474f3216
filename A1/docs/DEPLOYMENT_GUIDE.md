# Deployment Guide

This guide covers different deployment options for the Multi-Agent Internship Report Generator.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Local Development](#local-development)
3. [Streamlit Community Cloud](#streamlit-community-cloud)
4. [Docker Deployment](#docker-deployment)
5. [Production Deployment](#production-deployment)
6. [Environment Configuration](#environment-configuration)
7. [Monitoring and Maintenance](#monitoring-and-maintenance)

## Prerequisites

### Required Accounts and Services

1. **Google Cloud Console Account**
   - Create OAuth 2.0 credentials
   - Enable Google+ API

2. **GroqCloud Account**
   - Sign up at [GroqCloud](https://groq.com)
   - Get API key for Llama3-70B model

3. **GitHub Account**
   - For version control and CI/CD

4. **Streamlit Community Cloud Account** (for free hosting)
   - Connect to your GitHub account

### System Requirements

- Python 3.9 or higher
- Git
- Docker (for containerized deployment)
- 4GB+ RAM recommended
- 10GB+ disk space

## Local Development

### 1. Clone Repository

```bash
git clone https://github.com/yourusername/internship-report-generator.git
cd internship-report-generator
```

### 2. Set Up Python Environment

```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

### 3. Configure Environment Variables

```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your configuration
nano .env
```

Required environment variables:
```env
GROQ_API_KEY=your_groq_api_key
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
COOKIE_KEY=your_secure_random_key
```

### 4. Set Up Google OAuth

1. Go to [Google Cloud Console](https://console.cloud.google.com)
2. Create a new project or select existing one
3. Enable Google+ API
4. Create OAuth 2.0 credentials:
   - Application type: Web application
   - Authorized redirect URIs: `http://localhost:8501`
5. Download credentials and update `.env` file

### 5. Run Application

```bash
streamlit run streamlit_app/main.py
```

The application will be available at `http://localhost:8501`

## Streamlit Community Cloud

### 1. Prepare Repository

Ensure your repository has:
- `requirements.txt`
- `.streamlit/config.toml`
- `.streamlit/secrets.toml` (configured in Streamlit Cloud dashboard)

### 2. Deploy to Streamlit Cloud

1. Go to [Streamlit Cloud](https://streamlit.io/cloud)
2. Connect your GitHub account
3. Select your repository
4. Set main file path: `streamlit_app/main.py`
5. Configure secrets in the dashboard

### 3. Configure Secrets

In Streamlit Cloud dashboard, add these secrets:

```toml
[general]
GROQ_API_KEY = "your_groq_api_key"
GOOGLE_CLIENT_ID = "your_google_client_id"
GOOGLE_CLIENT_SECRET = "your_google_client_secret"
COOKIE_KEY = "your_secure_random_key"
```

### 4. Update OAuth Redirect URI

Update your Google OAuth credentials:
- Authorized redirect URIs: `https://your-app-name.streamlit.app`

## Docker Deployment

### 1. Build Docker Image

```bash
# Build image
docker build -f docker/Dockerfile -t internship-report-generator .

# Or use docker-compose
docker-compose -f docker/docker-compose.yml build
```

### 2. Run with Docker

```bash
# Simple run
docker run -p 8501:8501 \
  -e GROQ_API_KEY=your_key \
  -e GOOGLE_CLIENT_ID=your_client_id \
  -e GOOGLE_CLIENT_SECRET=your_secret \
  internship-report-generator

# Or use docker-compose
docker-compose -f docker/docker-compose.yml up -d
```

### 3. Environment Configuration

Create `.env` file for docker-compose:

```env
GROQ_API_KEY=your_groq_api_key
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
COOKIE_KEY=your_secure_random_key
```

## Production Deployment

### 1. Cloud Platforms

#### AWS Deployment

```bash
# Using AWS ECS
aws ecs create-cluster --cluster-name internship-report-generator

# Create task definition
aws ecs register-task-definition --cli-input-json file://aws-task-definition.json

# Create service
aws ecs create-service --cluster internship-report-generator \
  --service-name report-generator-service \
  --task-definition internship-report-generator:1 \
  --desired-count 1
```

#### Google Cloud Run

```bash
# Build and push to Google Container Registry
gcloud builds submit --tag gcr.io/PROJECT_ID/internship-report-generator

# Deploy to Cloud Run
gcloud run deploy internship-report-generator \
  --image gcr.io/PROJECT_ID/internship-report-generator \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated
```

#### Azure Container Instances

```bash
# Create resource group
az group create --name internship-report-rg --location eastus

# Deploy container
az container create \
  --resource-group internship-report-rg \
  --name internship-report-generator \
  --image your-registry/internship-report-generator:latest \
  --dns-name-label internship-report-unique \
  --ports 8501
```

### 2. Load Balancing and SSL

#### Nginx Configuration

```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl;
    server_name your-domain.com;

    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;

    location / {
        proxy_pass http://localhost:8501;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## Environment Configuration

### Production Environment Variables

```env
# Application
APP_NAME=Multi-Agent Internship Report Generator
ENVIRONMENT=production
DEBUG=false

# API Keys
GROQ_API_KEY=your_production_groq_key
GOOGLE_CLIENT_ID=your_production_client_id
GOOGLE_CLIENT_SECRET=your_production_client_secret

# Security
COOKIE_KEY=your_secure_production_key
SECRET_KEY=your_secret_key
SESSION_LIFETIME_HOURS=24

# Database
DATABASE_URL=postgresql://user:pass@host:port/dbname

# File Storage
UPLOAD_FOLDER=/app/data/uploads
MAX_FILE_SIZE_MB=50

# Email
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_app_password

# Monitoring
SENTRY_DSN=your_sentry_dsn
ENABLE_ANALYTICS=true

# Performance
MAX_CONCURRENT_REQUESTS=10
REQUEST_TIMEOUT_SECONDS=300
```

### Security Considerations

1. **API Keys**: Store in secure environment variables
2. **Database**: Use encrypted connections
3. **File Storage**: Implement proper access controls
4. **HTTPS**: Always use SSL/TLS in production
5. **Rate Limiting**: Implement to prevent abuse
6. **Input Validation**: Sanitize all user inputs

## Monitoring and Maintenance

### 1. Health Checks

```bash
# Application health
curl -f https://your-domain.com/_stcore/health

# Database health
curl -f https://your-domain.com/api/health/database

# AI services health
curl -f https://your-domain.com/api/health/ai
```

### 2. Logging

Configure structured logging:

```python
import logging
import structlog

logging.basicConfig(
    format="%(message)s",
    stream=sys.stdout,
    level=logging.INFO,
)

logger = structlog.get_logger()
```

### 3. Monitoring Stack

#### Prometheus + Grafana

```yaml
# docker-compose.monitoring.yml
version: '3.8'
services:
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml

  grafana:
    image: grafana/grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
```

### 4. Backup Strategy

```bash
# Database backup
pg_dump $DATABASE_URL > backup_$(date +%Y%m%d_%H%M%S).sql

# File storage backup
tar -czf uploads_backup_$(date +%Y%m%d_%H%M%S).tar.gz uploads/

# Configuration backup
cp .env config_backup_$(date +%Y%m%d_%H%M%S).env
```

### 5. Update Process

```bash
# 1. Backup current deployment
./scripts/backup.sh

# 2. Pull latest changes
git pull origin main

# 3. Update dependencies
pip install -r requirements.txt

# 4. Run tests
pytest tests/

# 5. Deploy with zero downtime
docker-compose up -d --no-deps app

# 6. Verify deployment
./scripts/health_check.sh
```

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Check Google OAuth configuration
   - Verify redirect URIs
   - Ensure API keys are correct

2. **File Upload Issues**
   - Check file size limits
   - Verify upload directory permissions
   - Check available disk space

3. **AI Processing Errors**
   - Verify GroqCloud API key
   - Check rate limits
   - Monitor API quotas

4. **Performance Issues**
   - Monitor resource usage
   - Check database performance
   - Review application logs

### Support

For deployment support:
- Check [GitHub Issues](https://github.com/yourusername/internship-report-generator/issues)
- Review [Documentation](https://github.com/yourusername/internship-report-generator/docs)
- Contact <NAME_EMAIL>
