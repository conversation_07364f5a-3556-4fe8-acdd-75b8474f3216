# GroqCloud API Configuration
GROQ_API_KEY=your_groq_api_key_here

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your_google_client_id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your_google_client_secret
GOOGLE_REDIRECT_URI=https://your-app-url.streamlit.app

# Email Configuration
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_app_password
EMAIL_FROM_NAME=Internship Report Generator

# Security Configuration
COOKIE_KEY=your_secure_random_cookie_key_32_chars
SECRET_KEY=your_secret_key_for_encryption
JWT_SECRET_KEY=your_jwt_secret_key

# Database Configuration
DATABASE_URL=sqlite:///./internship_reports.db
DATABASE_ECHO=False

# Application Configuration
APP_NAME=Multi-Agent Internship Report Generator
APP_VERSION=1.0.0
DEBUG=False
ENVIRONMENT=production

# File Upload Configuration
MAX_FILE_SIZE_MB=50
ALLOWED_FILE_TYPES=pdf,docx,txt,png,jpg,jpeg
UPLOAD_FOLDER=./uploads
TEMP_FOLDER=./temp

# AI Model Configuration
DEFAULT_MODEL=llama3-70b-8192
TEMPERATURE=0.7
MAX_TOKENS=4096
TOP_P=0.9

# Rate Limiting
RATE_LIMIT_PER_MINUTE=10
RATE_LIMIT_PER_HOUR=100

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=./logs/app.log
LOG_ROTATION=1 week
LOG_RETENTION=4 weeks

# Cache Configuration
CACHE_TYPE=simple
CACHE_DEFAULT_TIMEOUT=300

# Session Configuration
SESSION_LIFETIME_HOURS=24
SESSION_COOKIE_SECURE=True
SESSION_COOKIE_HTTPONLY=True

# Monitoring Configuration
SENTRY_DSN=your_sentry_dsn_here
ENABLE_ANALYTICS=True

# Cloud Storage (Optional)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_BUCKET_NAME=your_s3_bucket_name
AWS_REGION=us-east-1

# Google Cloud Storage (Optional)
GOOGLE_CLOUD_PROJECT=your_gcp_project_id
GOOGLE_CLOUD_BUCKET=your_gcs_bucket_name

# Feature Flags
ENABLE_EMAIL_DELIVERY=True
ENABLE_VISUAL_GENERATION=True
ENABLE_OCR=True
ENABLE_ANALYTICS=True
ENABLE_FEEDBACK=True

# Performance Configuration
MAX_CONCURRENT_REQUESTS=5
REQUEST_TIMEOUT_SECONDS=300
WORKER_PROCESSES=1

# Development Configuration
RELOAD_ON_CHANGE=False
SHOW_ERROR_DETAILS=False
