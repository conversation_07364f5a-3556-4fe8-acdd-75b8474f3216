version: '3.8'

services:
  # Main application service
  app:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: internship-report-generator
    ports:
      - "8501:8501"
    environment:
      # Application settings
      - APP_NAME=Multi-Agent Internship Report Generator
      - ENVIRONMENT=production
      - DEBUG=false
      
      # Database settings
      - DATABASE_URL=sqlite:///./data/internship_reports.db
      
      # File storage settings
      - UPLOAD_FOLDER=/app/data/uploads
      - TEMP_FOLDER=/app/data/temp
      - MAX_FILE_SIZE_MB=50
      
      # AI model settings
      - DEFAULT_MODEL=llama3-70b-8192
      - TEMPERATURE=0.7
      - MAX_TOKENS=4096
      
      # Security settings
      - SESSION_LIFETIME_HOURS=24
      - RATE_LIMIT_PER_MINUTE=10
      
      # Feature flags
      - ENABLE_EMAIL_DELIVERY=true
      - ENABLE_VISUAL_GENERATION=true
      - ENABLE_OCR=true
      - ENABLE_ANALYTICS=true
    
    volumes:
      # Persistent data storage
      - app_data:/app/data
      - app_logs:/app/logs
      
      # Configuration files (optional)
      - ./config:/app/config:ro
    
    networks:
      - app_network
    
    restart: unless-stopped
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8501/_stcore/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    depends_on:
      - redis
    
    # Resource limits
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '0.5'
          memory: 1G

  # Redis for caching and session storage
  redis:
    image: redis:7-alpine
    container_name: internship-report-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - app_network
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx reverse proxy (optional, for production)
  nginx:
    image: nginx:alpine
    container_name: internship-report-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    networks:
      - app_network
    restart: unless-stopped
    depends_on:
      - app
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring with Prometheus (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: internship-report-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - app_network
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  # Grafana for monitoring dashboards (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: internship-report-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - app_network
    restart: unless-stopped
    depends_on:
      - prometheus

  # Log aggregation with ELK stack (optional)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: internship-report-elasticsearch
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - app_network
    restart: unless-stopped

  logstash:
    image: docker.elastic.co/logstash/logstash:8.11.0
    container_name: internship-report-logstash
    volumes:
      - ./logging/logstash.conf:/usr/share/logstash/pipeline/logstash.conf:ro
      - app_logs:/app/logs:ro
    ports:
      - "5044:5044"
    networks:
      - app_network
    restart: unless-stopped
    depends_on:
      - elasticsearch

  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: internship-report-kibana
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    networks:
      - app_network
    restart: unless-stopped
    depends_on:
      - elasticsearch

# Named volumes for persistent data
volumes:
  app_data:
    driver: local
  app_logs:
    driver: local
  redis_data:
    driver: local
  nginx_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_data:
    driver: local

# Custom network
networks:
  app_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
