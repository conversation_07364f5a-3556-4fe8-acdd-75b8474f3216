"""
Multi-Agent Internship Report Editor & Generator
Demo Version - Main Streamlit Application Entry Point
"""

import streamlit as st
import pandas as pd
import plotly.express as px
from datetime import datetime, timedelta
import random

# Page configuration
st.set_page_config(
    page_title="Multi-Agent Internship Report Generator",
    page_icon="📄",
    layout="wide",
    initial_sidebar_state="expanded",
    menu_items={
        'Get Help': 'https://github.com/yourusername/internship-report-generator',
        'Report a bug': 'https://github.com/yourusername/internship-report-generator/issues',
        'About': """
        # Multi-Agent Internship Report Generator

        A production-ready AI-powered system that helps students worldwide
        create and enhance internship reports using advanced multi-agent technology.

        **Features:**
        - 5 specialized AI agents for comprehensive report enhancement
        - Google OAuth authentication for secure access
        - Professional document generation in multiple formats
        - Real-time processing with progress tracking
        - Email delivery with professional templates

        **Technology Stack:**
        - CrewAI Framework for multi-agent orchestration
        - GroqCloud API for fast AI processing
        - Streamlit for web interface
        - Google OAuth for authentication

        Made with ❤️ for students worldwide
        """
    }
)

# Load custom CSS
def load_css():
    """Load custom CSS styling"""
    st.markdown("""
    <style>
    .hero-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 4rem 2rem;
        border-radius: 20px;
        margin-bottom: 2rem;
        text-align: center;
    }
    .hero-title {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 1rem;
    }
    .hero-subtitle {
        font-size: 1.2rem;
        margin-bottom: 2rem;
        opacity: 0.9;
    }
    .feature-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
        margin-top: 2rem;
    }
    .feature-card {
        background: rgba(255, 255, 255, 0.1);
        padding: 2rem;
        border-radius: 15px;
        text-align: center;
    }
    .metric-card {
        background: white;
        padding: 1.5rem;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        border-left: 4px solid #FF6B6B;
    }
    .stButton > button {
        background: linear-gradient(135deg, #FF6B6B, #4ECDC4);
        color: white;
        border: none;
        border-radius: 10px;
        font-weight: 600;
    }
    </style>
    """, unsafe_allow_html=True)

# Initialize session state
def init_session_state():
    """Initialize session state variables"""
    if 'authenticated' not in st.session_state:
        st.session_state.authenticated = False
    if 'user_info' not in st.session_state:
        st.session_state.user_info = {'name': 'Demo User', 'email': '<EMAIL>', 'picture': ''}
    if 'current_page' not in st.session_state:
        st.session_state.current_page = 'dashboard'
    if 'demo_reports' not in st.session_state:
        # Generate demo data
        st.session_state.demo_reports = generate_demo_data()

def generate_demo_data():
    """Generate demo report data"""
    reports = []
    statuses = ['completed', 'processing', 'pending', 'failed']
    titles = [
        'Web Development Internship Report',
        'Data Science Project Analysis',
        'Mobile App Development Experience',
        'Machine Learning Research Report',
        'Software Engineering Internship',
        'UI/UX Design Portfolio',
        'Database Management Project',
        'Cloud Computing Implementation'
    ]

    for i in range(8):
        report = {
            'id': i + 1,
            'title': titles[i],
            'status': random.choice(statuses),
            'created_at': datetime.now() - timedelta(days=random.randint(1, 30)),
            'improvement_score': random.randint(65, 95) if random.choice([True, False]) else None,
            'processing_time': random.randint(120, 600) if random.choice([True, False]) else None
        }
        reports.append(report)

    return reports

def show_landing_page():
    """Display landing page for unauthenticated users"""
    st.markdown("""
    <div class="hero-section">
        <h1 class="hero-title">🤖 Multi-Agent Internship Report Generator</h1>
        <p class="hero-subtitle">
            Transform your internship reports with AI-powered enhancement and generation
        </p>
    </div>
    """, unsafe_allow_html=True)

    # Feature cards
    col1, col2, col3 = st.columns(3)

    with col1:
        st.markdown("""
        <div class="feature-card">
            <h3>📄 Document Enhancement</h3>
            <p>Upload existing reports for professional improvement using 5 specialized AI agents</p>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        st.markdown("""
        <div class="feature-card">
            <h3>🚀 Report Generation</h3>
            <p>Create new reports from scratch with AI assistance and real-time processing</p>
        </div>
        """, unsafe_allow_html=True)

    with col3:
        st.markdown("""
        <div class="feature-card">
            <h3>🌍 Global Access</h3>
            <p>Available to students worldwide with secure Google authentication</p>
        </div>
        """, unsafe_allow_html=True)

    st.markdown("---")

    # Demo authentication
    col1, col2, col3 = st.columns([1, 2, 1])

    with col2:
        st.markdown("### 🔐 Demo Access")
        st.info("This is a demo version. Click below to explore the interface!")

        if st.button("🚀 Enter Demo Mode", type="primary", use_container_width=True):
            st.session_state.authenticated = True
            st.rerun()

def show_dashboard():
    """Display main dashboard"""
    st.markdown("# 🏠 Dashboard")
    st.markdown("Welcome to your Multi-Agent Internship Report Generator dashboard!")

    # Quick stats
    col1, col2, col3, col4 = st.columns(4)

    reports = st.session_state.demo_reports
    completed_reports = [r for r in reports if r['status'] == 'completed']
    processing_reports = [r for r in reports if r['status'] in ['pending', 'processing']]

    with col1:
        st.metric("📄 Total Reports", len(reports), delta="+2 this week")

    with col2:
        st.metric("✅ Completed", len(completed_reports), delta=f"{len(completed_reports)/len(reports)*100:.0f}%")

    with col3:
        st.metric("⏳ Processing", len(processing_reports), delta="Active" if processing_reports else "None")

    with col4:
        scores = [r['improvement_score'] for r in completed_reports if r['improvement_score']]
        avg_score = sum(scores) / len(scores) if scores else 0
        st.metric("📈 Avg. Improvement", f"{avg_score:.1f}%", delta=f"Based on {len(scores)} reports")

    st.markdown("---")

    # Quick Actions
    st.markdown("## 🚀 Quick Actions")

    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("📤 Upload Report for Enhancement", use_container_width=True, type="primary"):
            st.session_state.current_page = 'upload'
            st.rerun()

    with col2:
        if st.button("📝 Generate New Report", use_container_width=True):
            st.session_state.current_page = 'generate'
            st.rerun()

    with col3:
        if st.button("📊 View Report History", use_container_width=True):
            st.session_state.current_page = 'history'
            st.rerun()

    st.markdown("---")

    # Recent Activity and Analytics
    col1, col2 = st.columns([2, 1])

    with col1:
        st.markdown("## 📋 Recent Reports")

        for report in reports[:5]:
            with st.container():
                report_col1, report_col2, report_col3 = st.columns([3, 1, 1])

                with report_col1:
                    st.markdown(f"**{report['title']}**")
                    st.caption(f"Created: {report['created_at'].strftime('%Y-%m-%d %H:%M')}")

                with report_col2:
                    status_colors = {
                        'completed': '🟢',
                        'processing': '🟡',
                        'pending': '🔵',
                        'failed': '🔴'
                    }
                    st.markdown(f"{status_colors.get(report['status'], '⚪')} {report['status'].title()}")

                with report_col3:
                    if report['status'] == 'completed':
                        if st.button("📥 Download", key=f"download_{report['id']}"):
                            st.success("Demo: File would be downloaded!")

                st.markdown("---")

    with col2:
        st.markdown("## 📊 Statistics")

        # Status distribution
        status_counts = {}
        for report in reports:
            status_counts[report['status']] = status_counts.get(report['status'], 0) + 1

        if status_counts:
            fig = px.pie(
                values=list(status_counts.values()),
                names=list(status_counts.keys()),
                title="Report Status Distribution"
            )
            fig.update_layout(height=300)
            st.plotly_chart(fig, use_container_width=True)

        # AI Agents Status
        st.markdown("### 🤖 AI Agents Status")
        agents = [
            "Document Analyzer",
            "Content Enhancer",
            "Structure Optimizer",
            "Visual Integrator",
            "Quality Assurance"
        ]

        for agent in agents:
            st.markdown(f"**{agent}**: 🟢 Online")

def show_upload_page():
    """Display upload page"""
    st.markdown("# 📤 Upload Report for Enhancement")
    st.markdown("Upload your existing internship report and let our AI agents enhance it professionally.")

    # File upload
    st.markdown("## 📁 Upload Your Document")
    uploaded_file = st.file_uploader(
        "Choose your internship report file",
        type=['pdf', 'docx', 'txt'],
        help="Supported formats: PDF, DOCX, TXT"
    )

    if uploaded_file:
        st.success(f"✅ File uploaded: {uploaded_file.name}")
        st.info(f"📊 File size: {uploaded_file.size / 1024 / 1024:.2f} MB")

        # Enhancement options
        st.markdown("## ⚙️ Enhancement Configuration")

        col1, col2 = st.columns(2)

        with col1:
            enhancement_level = st.selectbox(
                "Enhancement Level",
                options=['light', 'moderate', 'complete'],
                format_func=lambda x: {
                    'light': '🔧 Light Polish (5-10 min)',
                    'moderate': '🛠️ Moderate Enhancement (10-15 min)',
                    'complete': '🔨 Complete Rewrite (15-20 min)'
                }[x]
            )

            title = st.text_input("Report Title", value=uploaded_file.name.rsplit('.', 1)[0])

        with col2:
            focus_areas = st.multiselect(
                "Focus Areas",
                options=[
                    'technical_content',
                    'writing_style',
                    'structure_organization',
                    'formatting',
                    'visual_elements',
                    'professional_tone'
                ],
                default=['technical_content', 'writing_style'],
                format_func=lambda x: {
                    'technical_content': '🔬 Technical Content',
                    'writing_style': '✍️ Writing Style',
                    'structure_organization': '📋 Structure & Organization',
                    'formatting': '📄 Formatting',
                    'visual_elements': '🎨 Visual Elements',
                    'professional_tone': '💼 Professional Tone'
                }[x]
            )

        if st.button("🚀 Start Enhancement Process", type="primary", use_container_width=True):
            # Demo processing
            progress_bar = st.progress(0)
            status_text = st.empty()

            steps = [
                "📁 Analyzing document structure...",
                "✍️ Enhancing content quality...",
                "📋 Optimizing organization...",
                "🎨 Integrating visual elements...",
                "✅ Final quality assurance...",
                "📧 Preparing delivery..."
            ]

            for i, step in enumerate(steps):
                status_text.text(step)
                progress_bar.progress((i + 1) / len(steps))
                st.time.sleep(1)

            st.success("🎉 Enhancement completed successfully!")
            st.balloons()

            col1, col2 = st.columns(2)
            with col1:
                st.metric("Processing Time", "8.3 seconds")
                st.metric("Improvement Score", "87%")

            with col2:
                if st.button("📥 Download Enhanced Report"):
                    st.success("Demo: Enhanced report would be downloaded!")

def show_generate_page():
    """Display report generation page"""
    st.markdown("# 📝 Generate New Report")
    st.markdown("Create a professional internship report from scratch using AI assistance.")

    st.info("🚧 Report generation feature coming soon! This will allow you to create reports from templates and guided prompts.")

def show_history_page():
    """Display report history"""
    st.markdown("# 📊 Report History")
    st.markdown("View and manage all your processed reports.")

    # Display reports table
    reports_data = []
    for report in st.session_state.demo_reports:
        reports_data.append({
            'Title': report['title'],
            'Status': report['status'].title(),
            'Created': report['created_at'].strftime('%Y-%m-%d'),
            'Score': f"{report['improvement_score']}%" if report['improvement_score'] else "N/A"
        })

    df = pd.DataFrame(reports_data)
    st.dataframe(df, use_container_width=True)

def show_settings_page():
    """Display settings page"""
    st.markdown("# ⚙️ Settings")
    st.markdown("Configure your preferences and account settings.")

    st.info("🚧 Settings page coming soon! This will include preferences for AI processing, notifications, and account management.")

def show_main_app():
    """Display main application for authenticated users"""
    # Sidebar navigation
    with st.sidebar:
        st.markdown(f"### Welcome, {st.session_state.user_info.get('name', 'User')}!")

        st.markdown("---")

        # Navigation menu
        pages = {
            "🏠 Dashboard": "dashboard",
            "📤 Upload Report": "upload",
            "📝 Generate Report": "generate",
            "📊 Report History": "history",
            "⚙️ Settings": "settings"
        }

        for page_name, page_key in pages.items():
            if st.button(page_name, use_container_width=True):
                st.session_state.current_page = page_key
                st.rerun()

        st.markdown("---")

        # Logout button
        if st.button("🚪 Exit Demo", use_container_width=True):
            st.session_state.authenticated = False
            st.session_state.current_page = 'dashboard'
            st.rerun()

    # Main content area
    if st.session_state.current_page == 'dashboard':
        show_dashboard()
    elif st.session_state.current_page == 'upload':
        show_upload_page()
    elif st.session_state.current_page == 'generate':
        show_generate_page()
    elif st.session_state.current_page == 'history':
        show_history_page()
    elif st.session_state.current_page == 'settings':
        show_settings_page()

def main():
    """Main application function"""
    # Load custom styling
    load_css()

    # Initialize session state
    init_session_state()

    # Check authentication status
    if not st.session_state.authenticated:
        show_landing_page()
    else:
        show_main_app()

if __name__ == "__main__":
    main()
