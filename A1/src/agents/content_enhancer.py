"""
Content Enhancement Agent
Specialized agent for improving technical writing and content quality
"""

import re
import json
from typing import Dict, Any, List, Optional
from pathlib import Path

from crewai.tools import BaseTool
from pydantic import BaseModel, Field

class ContentRewriterTool(BaseTool):
    """Tool for rewriting and enhancing content quality"""
    name: str = "content_rewriter"
    description: str = "Rewrite content to improve clarity, professionalism, and technical accuracy"

    def _run(self, content: str, enhancement_level: str = "moderate") -> Dict[str, Any]:
        """Rewrite content based on enhancement level"""
        try:
            if enhancement_level == "light":
                return self._light_enhancement(content)
            elif enhancement_level == "moderate":
                return self._moderate_enhancement(content)
            elif enhancement_level == "complete":
                return self._complete_rewrite(content)
            else:
                return {"error": f"Unknown enhancement level: {enhancement_level}"}
        except Exception as e:
            return {"error": f"Content rewriting failed: {str(e)}"}

    def _light_enhancement(self, content: str) -> Dict[str, Any]:
        """Light enhancement: grammar, spelling, basic improvements"""
        enhanced_content = content
        changes = []

        # Fix common grammar issues
        grammar_fixes = [
            (r'\bi\b', 'I'),  # Capitalize 'i'
            (r'\s+', ' '),    # Multiple spaces to single space
            (r'\.{2,}', '.'), # Multiple periods to single
            (r'\s+\.', '.'),  # Space before period
            (r'\s+,', ','),   # Space before comma
        ]

        for pattern, replacement in grammar_fixes:
            if re.search(pattern, enhanced_content):
                enhanced_content = re.sub(pattern, replacement, enhanced_content)
                changes.append(f"Fixed grammar: {pattern} -> {replacement}")

        # Improve sentence structure
        sentences = enhanced_content.split('.')
        improved_sentences = []

        for sentence in sentences:
            sentence = sentence.strip()
            if sentence:
                # Capitalize first letter
                sentence = sentence[0].upper() + sentence[1:] if len(sentence) > 1 else sentence.upper()
                improved_sentences.append(sentence)

        enhanced_content = '. '.join(improved_sentences)
        if enhanced_content and not enhanced_content.endswith('.'):
            enhanced_content += '.'

        return {
            "enhanced_content": enhanced_content,
            "changes_made": changes,
            "enhancement_level": "light",
            "improvement_score": self._calculate_improvement_score(content, enhanced_content)
        }

    def _moderate_enhancement(self, content: str) -> Dict[str, Any]:
        """Moderate enhancement: structure, flow, technical terminology"""
        # Start with light enhancement
        light_result = self._light_enhancement(content)
        enhanced_content = light_result["enhanced_content"]
        changes = light_result["changes_made"]

        # Improve technical terminology
        tech_improvements = {
            r'\bapp\b': 'application',
            r'\bdb\b': 'database',
            r'\bapi\b': 'API',
            r'\bui\b': 'user interface',
            r'\bux\b': 'user experience',
            r'\bfrontend\b': 'front-end',
            r'\bbackend\b': 'back-end',
            r'\bwebsite\b': 'web application',
            r'\bcode\b': 'source code',
            r'\bbugs\b': 'software defects',
            r'\bfix\b': 'resolve',
            r'\bmake\b': 'develop',
            r'\bbuild\b': 'implement'
        }

        for pattern, replacement in tech_improvements.items():
            if re.search(pattern, enhanced_content, re.IGNORECASE):
                enhanced_content = re.sub(pattern, replacement, enhanced_content, flags=re.IGNORECASE)
                changes.append(f"Improved terminology: {pattern} -> {replacement}")

        # Enhance sentence structure and flow
        paragraphs = enhanced_content.split('\n\n')
        improved_paragraphs = []

        for paragraph in paragraphs:
            if paragraph.strip():
                # Add transition words and improve flow
                improved_paragraph = self._improve_paragraph_flow(paragraph)
                improved_paragraphs.append(improved_paragraph)

        enhanced_content = '\n\n'.join(improved_paragraphs)
        changes.append("Improved paragraph flow and transitions")

        # Add professional language patterns
        enhanced_content = self._add_professional_language(enhanced_content)
        changes.append("Enhanced professional language")

        return {
            "enhanced_content": enhanced_content,
            "changes_made": changes,
            "enhancement_level": "moderate",
            "improvement_score": self._calculate_improvement_score(content, enhanced_content)
        }

    def _complete_rewrite(self, content: str) -> Dict[str, Any]:
        """Complete rewrite: comprehensive restructuring and enhancement"""
        # Start with moderate enhancement
        moderate_result = self._moderate_enhancement(content)
        enhanced_content = moderate_result["enhanced_content"]
        changes = moderate_result["changes_made"]

        # Comprehensive restructuring
        sections = self._identify_content_sections(enhanced_content)
        restructured_sections = []

        for section in sections:
            restructured_section = self._rewrite_section_professionally(section)
            restructured_sections.append(restructured_section)

        enhanced_content = '\n\n'.join(restructured_sections)
        changes.append("Complete content restructuring")

        # Add technical depth and detail
        enhanced_content = self._add_technical_depth(enhanced_content)
        changes.append("Added technical depth and detail")

        # Ensure academic/professional standards
        enhanced_content = self._ensure_academic_standards(enhanced_content)
        changes.append("Applied academic and professional standards")

        return {
            "enhanced_content": enhanced_content,
            "changes_made": changes,
            "enhancement_level": "complete",
            "improvement_score": self._calculate_improvement_score(content, enhanced_content)
        }

    def _improve_paragraph_flow(self, paragraph: str) -> str:
        """Improve flow within a paragraph"""
        sentences = paragraph.split('.')
        if len(sentences) < 2:
            return paragraph

        # Add transition words
        transitions = [
            "Furthermore,", "Additionally,", "Moreover,", "Subsequently,",
            "Consequently,", "As a result,", "In addition,", "Therefore,"
        ]

        improved_sentences = [sentences[0]]
        for i, sentence in enumerate(sentences[1:], 1):
            sentence = sentence.strip()
            if sentence and i < len(sentences) - 1:
                # Occasionally add transitions
                if i % 3 == 0 and len(sentence.split()) > 5:
                    transition = transitions[i % len(transitions)]
                    sentence = f"{transition} {sentence.lower()}"
                improved_sentences.append(sentence)

        return '. '.join(improved_sentences)

    def _add_professional_language(self, content: str) -> str:
        """Add professional language patterns"""
        professional_replacements = {
            r'\bI did\b': 'I successfully implemented',
            r'\bI made\b': 'I developed',
            r'\bI worked on\b': 'I collaborated on',
            r'\bI learned\b': 'I acquired expertise in',
            r'\bI used\b': 'I utilized',
            r'\bI helped\b': 'I contributed to',
            r'\bI fixed\b': 'I resolved',
            r'\bI created\b': 'I designed and implemented',
            r'\bIt was good\b': 'The experience was valuable',
            r'\bIt was hard\b': 'The project presented significant challenges'
        }

        for pattern, replacement in professional_replacements.items():
            content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)

        return content

    def _identify_content_sections(self, content: str) -> List[str]:
        """Identify and separate content sections"""
        # Split by double newlines or common section indicators
        sections = re.split(r'\n\n+', content)
        return [section.strip() for section in sections if section.strip()]

    def _rewrite_section_professionally(self, section: str) -> str:
        """Rewrite a section with professional standards"""
        # Add more sophisticated sentence structures
        sentences = section.split('.')
        professional_sentences = []

        for sentence in sentences:
            sentence = sentence.strip()
            if sentence:
                # Make sentences more sophisticated
                if len(sentence.split()) < 8:
                    # Expand short sentences
                    sentence = self._expand_sentence(sentence)
                professional_sentences.append(sentence)

        return '. '.join(professional_sentences)

    def _expand_sentence(self, sentence: str) -> str:
        """Expand short sentences with more detail"""
        # Add context and detail to short sentences
        if 'implemented' in sentence.lower():
            return f"{sentence}, ensuring robust functionality and optimal performance"
        elif 'developed' in sentence.lower():
            return f"{sentence}, following industry best practices and design patterns"
        elif 'learned' in sentence.lower():
            return f"{sentence}, gaining valuable insights into professional development methodologies"
        else:
            return sentence

    def _add_technical_depth(self, content: str) -> str:
        """Add technical depth and professional terminology"""
        # Add technical context where appropriate
        technical_enhancements = {
            r'\bdatabase\b': 'relational database management system',
            r'\bwebsite\b': 'web application with responsive design',
            r'\bcode\b': 'well-documented source code',
            r'\btesting\b': 'comprehensive testing including unit and integration tests',
            r'\bdeployment\b': 'production deployment with CI/CD pipeline'
        }

        for pattern, replacement in technical_enhancements.items():
            content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)

        return content

    def _ensure_academic_standards(self, content: str) -> str:
        """Ensure content meets academic and professional standards"""
        # Ensure proper paragraph structure
        paragraphs = content.split('\n\n')
        academic_paragraphs = []

        for paragraph in paragraphs:
            if paragraph.strip():
                # Ensure each paragraph has a clear topic sentence
                sentences = paragraph.split('.')
                if len(sentences) > 1:
                    # Strengthen the first sentence as topic sentence
                    first_sentence = sentences[0].strip()
                    if first_sentence and not any(word in first_sentence.lower() for word in ['during', 'throughout', 'in this']):
                        first_sentence = f"During this phase of the internship, {first_sentence.lower()}"

                    sentences[0] = first_sentence
                    paragraph = '. '.join(sentences)

                academic_paragraphs.append(paragraph)

        return '\n\n'.join(academic_paragraphs)

    def _calculate_improvement_score(self, original: str, enhanced: str) -> float:
        """Calculate improvement score based on various metrics"""
        if not original or not enhanced:
            return 0.0

        # Calculate various improvement metrics
        original_words = len(original.split())
        enhanced_words = len(enhanced.split())

        # Word count improvement (moderate increase is good)
        word_improvement = min(100, (enhanced_words / original_words) * 50) if original_words > 0 else 0

        # Professional terminology count
        professional_terms = [
            'implemented', 'developed', 'collaborated', 'utilized', 'contributed',
            'designed', 'optimized', 'integrated', 'analyzed', 'evaluated'
        ]

        original_prof_count = sum(1 for term in professional_terms if term in original.lower())
        enhanced_prof_count = sum(1 for term in professional_terms if term in enhanced.lower())

        prof_improvement = min(100, (enhanced_prof_count / max(1, original_prof_count)) * 30)

        # Sentence structure improvement (average sentence length)
        original_sentences = len([s for s in original.split('.') if s.strip()])
        enhanced_sentences = len([s for s in enhanced.split('.') if s.strip()])

        if original_sentences > 0 and enhanced_sentences > 0:
            orig_avg_length = original_words / original_sentences
            enh_avg_length = enhanced_words / enhanced_sentences
            structure_improvement = min(100, (enh_avg_length / orig_avg_length) * 20)
        else:
            structure_improvement = 0

        # Overall improvement score
        total_score = word_improvement + prof_improvement + structure_improvement
        return min(100.0, total_score)

class TechnicalEnhancerTool(BaseTool):
    """Tool for enhancing technical descriptions and terminology"""
    name: str = "technical_enhancer"
    description: str = "Enhance technical content with proper terminology and detailed descriptions"

    def _run(self, content: str, focus_areas: List[str] = None) -> Dict[str, Any]:
        """Enhance technical aspects of content"""
        try:
            enhanced_content = content
            changes = []

            if not focus_areas:
                focus_areas = ['technical_content', 'terminology']

            if 'technical_content' in focus_areas:
                enhanced_content, tech_changes = self._enhance_technical_content(enhanced_content)
                changes.extend(tech_changes)

            if 'terminology' in focus_areas:
                enhanced_content, term_changes = self._improve_terminology(enhanced_content)
                changes.extend(term_changes)

            return {
                "enhanced_content": enhanced_content,
                "changes_made": changes,
                "technical_score": self._calculate_technical_score(content, enhanced_content)
            }
        except Exception as e:
            return {"error": f"Technical enhancement failed: {str(e)}"}

    def _enhance_technical_content(self, content: str) -> tuple[str, List[str]]:
        """Enhance technical content with detailed descriptions"""
        changes = []

        # Technical enhancement patterns
        tech_patterns = {
            r'\bHTML\b': 'HTML5 with semantic markup',
            r'\bCSS\b': 'CSS3 with responsive design principles',
            r'\bJavaScript\b': 'modern JavaScript (ES6+)',
            r'\bPython\b': 'Python programming language',
            r'\bReact\b': 'React.js framework',
            r'\bNode\.js\b': 'Node.js runtime environment',
            r'\bSQL\b': 'SQL database queries',
            r'\bGit\b': 'Git version control system',
            r'\bAPI\b': 'RESTful API',
            r'\bframework\b': 'development framework'
        }

        for pattern, replacement in tech_patterns.items():
            if re.search(pattern, content):
                content = re.sub(pattern, replacement, content)
                changes.append(f"Enhanced technical term: {pattern} -> {replacement}")

        return content, changes

    def _improve_terminology(self, content: str) -> tuple[str, List[str]]:
        """Improve technical terminology throughout content"""
        changes = []

        # Professional terminology improvements
        terminology_map = {
            r'\bwebsite\b': 'web application',
            r'\bapp\b': 'application',
            r'\bprogram\b': 'software application',
            r'\bcode\b': 'source code',
            r'\bserver\b': 'web server',
            r'\bdatabase\b': 'database management system',
            r'\buser\b': 'end user',
            r'\binterface\b': 'user interface',
            r'\bdesign\b': 'system design',
            r'\btesting\b': 'quality assurance testing'
        }

        for pattern, replacement in terminology_map.items():
            if re.search(pattern, content, re.IGNORECASE):
                content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)
                changes.append(f"Improved terminology: {pattern} -> {replacement}")

        return content, changes

    def _calculate_technical_score(self, original: str, enhanced: str) -> float:
        """Calculate technical enhancement score"""
        technical_terms = [
            'implementation', 'architecture', 'framework', 'methodology',
            'optimization', 'integration', 'scalability', 'performance',
            'security', 'authentication', 'authorization', 'encryption'
        ]

        original_count = sum(1 for term in technical_terms if term in original.lower())
        enhanced_count = sum(1 for term in technical_terms if term in enhanced.lower())

        if original_count == 0:
            return min(100.0, enhanced_count * 10)

        improvement_ratio = enhanced_count / original_count
        return min(100.0, improvement_ratio * 50)

class FlowOptimizerTool(BaseTool):
    """Tool for optimizing content flow and coherence"""
    name: str = "flow_optimizer"
    description: str = "Optimize content flow, transitions, and overall coherence"

    def _run(self, content: str) -> Dict[str, Any]:
        """Optimize content flow and coherence"""
        try:
            optimized_content, changes = self._optimize_flow(content)

            return {
                "optimized_content": optimized_content,
                "changes_made": changes,
                "flow_score": self._calculate_flow_score(content, optimized_content)
            }
        except Exception as e:
            return {"error": f"Flow optimization failed: {str(e)}"}

    def _optimize_flow(self, content: str) -> tuple[str, List[str]]:
        """Optimize the flow of content"""
        changes = []

        # Split into paragraphs
        paragraphs = [p.strip() for p in content.split('\n\n') if p.strip()]

        if len(paragraphs) < 2:
            return content, ["Content too short for flow optimization"]

        optimized_paragraphs = []

        for i, paragraph in enumerate(paragraphs):
            if i == 0:
                # First paragraph - ensure strong opening
                optimized_paragraph = self._optimize_opening_paragraph(paragraph)
                optimized_paragraphs.append(optimized_paragraph)
            elif i == len(paragraphs) - 1:
                # Last paragraph - ensure strong conclusion
                optimized_paragraph = self._optimize_closing_paragraph(paragraph)
                optimized_paragraphs.append(optimized_paragraph)
            else:
                # Middle paragraphs - add transitions
                optimized_paragraph = self._add_transitions(paragraph, i)
                optimized_paragraphs.append(optimized_paragraph)

        optimized_content = '\n\n'.join(optimized_paragraphs)
        changes.append("Optimized paragraph flow and transitions")

        return optimized_content, changes

    def _optimize_opening_paragraph(self, paragraph: str) -> str:
        """Optimize the opening paragraph"""
        if not paragraph.lower().startswith(('during', 'throughout', 'this report', 'my internship')):
            return f"This report details my internship experience, during which {paragraph.lower()}"
        return paragraph

    def _optimize_closing_paragraph(self, paragraph: str) -> str:
        """Optimize the closing paragraph"""
        if not any(word in paragraph.lower() for word in ['conclusion', 'summary', 'overall', 'in summary']):
            return f"In conclusion, {paragraph.lower()}"
        return paragraph

    def _add_transitions(self, paragraph: str, position: int) -> str:
        """Add appropriate transitions to paragraphs"""
        transitions = [
            "Furthermore,", "Additionally,", "Moreover,", "Subsequently,",
            "In addition to this,", "Building upon this experience,", "As the project progressed,",
            "During this phase,", "Following this implementation,", "As a result of this work,"
        ]

        # Don't add transition if paragraph already starts with one
        if any(paragraph.lower().startswith(t.lower()) for t in transitions):
            return paragraph

        # Select appropriate transition based on position
        transition = transitions[position % len(transitions)]
        return f"{transition} {paragraph.lower()}"

    def _calculate_flow_score(self, original: str, optimized: str) -> float:
        """Calculate flow improvement score"""
        # Count transition words
        transition_words = [
            'furthermore', 'additionally', 'moreover', 'subsequently',
            'however', 'therefore', 'consequently', 'meanwhile',
            'in addition', 'as a result', 'for example', 'in conclusion'
        ]

        original_transitions = sum(1 for word in transition_words if word in original.lower())
        optimized_transitions = sum(1 for word in transition_words if word in optimized.lower())

        if original_transitions == 0:
            return min(100.0, optimized_transitions * 15)

        improvement_ratio = optimized_transitions / original_transitions
        return min(100.0, improvement_ratio * 60)

class TerminologyCheckerTool(BaseTool):
    """Tool for checking and improving terminology consistency"""
    name: str = "terminology_checker"
    description: str = "Check and improve terminology consistency throughout the document"

    def _run(self, content: str) -> Dict[str, Any]:
        """Check and improve terminology consistency"""
        try:
            consistent_content, changes = self._ensure_consistency(content)

            return {
                "consistent_content": consistent_content,
                "changes_made": changes,
                "consistency_score": self._calculate_consistency_score(content, consistent_content)
            }
        except Exception as e:
            return {"error": f"Terminology checking failed: {str(e)}"}

    def _ensure_consistency(self, content: str) -> tuple[str, List[str]]:
        """Ensure terminology consistency"""
        changes = []

        # Define consistent terminology
        consistency_rules = {
            # Technical terms
            r'\bweb site\b': 'website',
            r'\bweb-site\b': 'website',
            r'\be-mail\b': 'email',
            r'\bE-mail\b': 'email',
            r'\bdata base\b': 'database',
            r'\bdata-base\b': 'database',

            # Professional terms
            r'\bfront end\b': 'frontend',
            r'\bfront-end\b': 'frontend',
            r'\bback end\b': 'backend',
            r'\bback-end\b': 'backend',

            # Consistency in capitalization
            r'\bapi\b': 'API',
            r'\bhtml\b': 'HTML',
            r'\bcss\b': 'CSS',
            r'\bsql\b': 'SQL',
            r'\bui\b': 'UI',
            r'\bux\b': 'UX',
        }

        for pattern, replacement in consistency_rules.items():
            if re.search(pattern, content, re.IGNORECASE):
                content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)
                changes.append(f"Ensured consistency: {pattern} -> {replacement}")

        return content, changes

    def _calculate_consistency_score(self, original: str, consistent: str) -> float:
        """Calculate terminology consistency score"""
        # Simple metric based on number of corrections made
        original_words = len(original.split())
        if original_words == 0:
            return 100.0

        # Count inconsistencies found and fixed
        inconsistencies_fixed = len([change for change in self._ensure_consistency(original)[1]])

        # Higher score for fewer inconsistencies
        consistency_ratio = max(0, 1 - (inconsistencies_fixed / original_words))
        return consistency_ratio * 100

class ContentEnhancer:
    """Content Enhancement Agent - main class"""

    def __init__(self):
        """Initialize content enhancer with tools"""
        self.tools = [
            ContentRewriterTool(),
            TechnicalEnhancerTool(),
            FlowOptimizerTool(),
            TerminologyCheckerTool()
        ]

    def get_tools(self) -> List[BaseTool]:
        """Get list of available tools"""
        return self.tools

    def enhance_content(self, content: str, enhancement_level: str = "moderate",
                       focus_areas: List[str] = None) -> Dict[str, Any]:
        """Main method to enhance content"""
        try:
            if not focus_areas:
                focus_areas = ['technical_content', 'writing_style']

            # Step 1: Content rewriting
            rewriter_tool = self.tools[0]
            rewrite_result = rewriter_tool._run(content, enhancement_level)

            if "error" in rewrite_result:
                return rewrite_result

            enhanced_content = rewrite_result["enhanced_content"]
            all_changes = rewrite_result["changes_made"]

            # Step 2: Technical enhancement
            if 'technical_content' in focus_areas:
                tech_tool = self.tools[1]
                tech_result = tech_tool._run(enhanced_content, focus_areas)

                if "error" not in tech_result:
                    enhanced_content = tech_result["enhanced_content"]
                    all_changes.extend(tech_result["changes_made"])

            # Step 3: Flow optimization
            if 'writing_style' in focus_areas:
                flow_tool = self.tools[2]
                flow_result = flow_tool._run(enhanced_content)

                if "error" not in flow_result:
                    enhanced_content = flow_result["optimized_content"]
                    all_changes.extend(flow_result["changes_made"])

            # Step 4: Terminology consistency
            terminology_tool = self.tools[3]
            term_result = terminology_tool._run(enhanced_content)

            if "error" not in term_result:
                enhanced_content = term_result["consistent_content"]
                all_changes.extend(term_result["changes_made"])

            # Compile final results
            final_result = {
                "enhanced_content": enhanced_content,
                "original_content": content,
                "changes_made": all_changes,
                "enhancement_level": enhancement_level,
                "focus_areas": focus_areas,
                "improvement_score": rewrite_result.get("improvement_score", 0),
                "technical_score": tech_result.get("technical_score", 0) if 'tech_result' in locals() else 0,
                "flow_score": flow_result.get("flow_score", 0) if 'flow_result' in locals() else 0,
                "consistency_score": term_result.get("consistency_score", 0) if 'term_result' in locals() else 0
            }

            return final_result

        except Exception as e:
            return {"error": f"Content enhancement failed: {str(e)}"}
