#!/bin/bash

# Multi-Agent Internship Report Generator Setup Script
# This script helps set up the development environment

set -e

echo "🚀 Multi-Agent Internship Report Generator Setup"
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Check if Python is installed
check_python() {
    print_step "Checking Python installation..."
    
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
        print_status "Python $PYTHON_VERSION found"
        
        # Check if version is 3.9 or higher
        if python3 -c "import sys; exit(0 if sys.version_info >= (3, 9) else 1)"; then
            print_status "Python version is compatible (3.9+)"
        else
            print_error "Python 3.9 or higher is required. Current version: $PYTHON_VERSION"
            exit 1
        fi
    else
        print_error "Python 3 is not installed. Please install Python 3.9 or higher."
        exit 1
    fi
}

# Check if Git is installed
check_git() {
    print_step "Checking Git installation..."
    
    if command -v git &> /dev/null; then
        GIT_VERSION=$(git --version | cut -d' ' -f3)
        print_status "Git $GIT_VERSION found"
    else
        print_error "Git is not installed. Please install Git."
        exit 1
    fi
}

# Create virtual environment
create_venv() {
    print_step "Creating Python virtual environment..."
    
    if [ ! -d "venv" ]; then
        python3 -m venv venv
        print_status "Virtual environment created"
    else
        print_warning "Virtual environment already exists"
    fi
}

# Activate virtual environment and install dependencies
install_dependencies() {
    print_step "Installing Python dependencies..."
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Upgrade pip
    pip install --upgrade pip
    
    # Install requirements
    if [ -f "requirements.txt" ]; then
        pip install -r requirements.txt
        print_status "Dependencies installed successfully"
    else
        print_error "requirements.txt not found"
        exit 1
    fi
}

# Install system dependencies
install_system_deps() {
    print_step "Checking system dependencies..."
    
    # Check operating system
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        if command -v apt-get &> /dev/null; then
            print_status "Installing system dependencies with apt-get..."
            sudo apt-get update
            sudo apt-get install -y tesseract-ocr tesseract-ocr-eng libmagic1
        elif command -v yum &> /dev/null; then
            print_status "Installing system dependencies with yum..."
            sudo yum install -y tesseract tesseract-langpack-eng file-devel
        else
            print_warning "Package manager not detected. Please install tesseract-ocr and libmagic manually."
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if command -v brew &> /dev/null; then
            print_status "Installing system dependencies with Homebrew..."
            brew install tesseract libmagic
        else
            print_warning "Homebrew not found. Please install tesseract and libmagic manually."
        fi
    else
        print_warning "Operating system not detected. Please install tesseract-ocr and libmagic manually."
    fi
}

# Create environment file
create_env_file() {
    print_step "Setting up environment configuration..."
    
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            cp .env.example .env
            print_status "Environment file created from template"
            print_warning "Please edit .env file with your API keys and configuration"
        else
            print_error ".env.example not found"
            exit 1
        fi
    else
        print_warning ".env file already exists"
    fi
}

# Create necessary directories
create_directories() {
    print_step "Creating necessary directories..."
    
    mkdir -p uploads temp logs data
    print_status "Directories created"
}

# Run tests
run_tests() {
    print_step "Running tests..."
    
    source venv/bin/activate
    
    if command -v pytest &> /dev/null; then
        pytest tests/ -v
        print_status "Tests completed"
    else
        print_warning "pytest not found. Installing..."
        pip install pytest
        pytest tests/ -v
        print_status "Tests completed"
    fi
}

# Display setup completion message
show_completion_message() {
    echo ""
    echo "🎉 Setup completed successfully!"
    echo "================================"
    echo ""
    echo "Next steps:"
    echo "1. Edit the .env file with your API keys:"
    echo "   - GROQ_API_KEY (get from https://groq.com)"
    echo "   - GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET (from Google Cloud Console)"
    echo "   - COOKIE_KEY (generate a secure random string)"
    echo ""
    echo "2. Set up Google OAuth:"
    echo "   - Go to Google Cloud Console"
    echo "   - Create OAuth 2.0 credentials"
    echo "   - Add http://localhost:8501 as authorized redirect URI"
    echo ""
    echo "3. Start the application:"
    echo "   source venv/bin/activate"
    echo "   streamlit run streamlit_app/main.py"
    echo ""
    echo "4. Open your browser and go to http://localhost:8501"
    echo ""
    echo "For deployment instructions, see docs/DEPLOYMENT_GUIDE.md"
    echo ""
}

# Main setup function
main() {
    echo "Starting setup process..."
    echo ""
    
    check_python
    check_git
    install_system_deps
    create_venv
    install_dependencies
    create_env_file
    create_directories
    
    # Ask if user wants to run tests
    read -p "Do you want to run tests? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        run_tests
    fi
    
    show_completion_message
}

# Check if script is being run from the correct directory
if [ ! -f "requirements.txt" ] || [ ! -f "streamlit_app/main.py" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

# Run main function
main
